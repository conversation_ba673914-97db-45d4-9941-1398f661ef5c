using CryptoTradingBot.Models;

namespace CryptoTradingBot.Services
{
    /// <summary>
    /// واجهة خدمة إدارة المحفظة
    /// </summary>
    public interface IPortfolioService
    {
        /// <summary>
        /// الحصول على محفظة المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>تفاصيل المحفظة</returns>
        Task<Portfolio> GetPortfolioAsync(string userId);

        /// <summary>
        /// تحديث محفظة المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>المحفظة المحدثة</returns>
        Task<Portfolio> UpdatePortfolioAsync(string userId);

        /// <summary>
        /// الحصول على توزيع المحفظة
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>توزيع الأصول</returns>
        Task<List<AssetAllocation>> GetAssetAllocationAsync(string userId);

        /// <summary>
        /// حساب أداء المحفظة
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="days">عدد الأيام للمقارنة</param>
        /// <returns>أداء المحفظة</returns>
        Task<PortfolioPerformance> GetPortfolioPerformanceAsync(string userId, int days = 30);

        /// <summary>
        /// إعادة توازن المحفظة
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="targetAllocations">التوزيع المستهدف</param>
        /// <returns>نتيجة إعادة التوازن</returns>
        Task<RebalanceResult> RebalancePortfolioAsync(string userId, List<TargetAllocation> targetAllocations);

        /// <summary>
        /// تحليل مخاطر المحفظة
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>تحليل المخاطر</returns>
        Task<RiskAnalysis> AnalyzePortfolioRiskAsync(string userId);
    }

    /// <summary>
    /// نموذج المحفظة
    /// </summary>
    public class Portfolio
    {
        public string UserId { get; set; } = string.Empty;
        public decimal TotalValue { get; set; }
        public decimal TotalCost { get; set; }
        public decimal TotalProfitLoss { get; set; }
        public decimal TotalProfitLossPercentage { get; set; }
        public decimal AvailableCash { get; set; }
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        public List<PortfolioHolding> Holdings { get; set; } = new();
    }

    /// <summary>
    /// نموذج حيازة في المحفظة
    /// </summary>
    public class PortfolioHolding
    {
        public string Symbol { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public decimal AveragePrice { get; set; }
        public decimal CurrentPrice { get; set; }
        public decimal TotalCost { get; set; }
        public decimal CurrentValue { get; set; }
        public decimal ProfitLoss { get; set; }
        public decimal ProfitLossPercentage { get; set; }
        public decimal AllocationPercentage { get; set; }
    }

    /// <summary>
    /// نموذج توزيع الأصول
    /// </summary>
    public class AssetAllocation
    {
        public string Symbol { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public decimal Percentage { get; set; }
        public string Category { get; set; } = string.Empty; // Large Cap, Mid Cap, etc.
    }

    /// <summary>
    /// نموذج أداء المحفظة
    /// </summary>
    public class PortfolioPerformance
    {
        public decimal CurrentValue { get; set; }
        public decimal PreviousValue { get; set; }
        public decimal Change { get; set; }
        public decimal ChangePercentage { get; set; }
        public decimal HighestValue { get; set; }
        public decimal LowestValue { get; set; }
        public decimal Volatility { get; set; }
        public decimal SharpeRatio { get; set; }
        public int DaysAnalyzed { get; set; }
        public List<PerformanceDataPoint> HistoricalPerformance { get; set; } = new();
    }

    /// <summary>
    /// نقطة بيانات الأداء
    /// </summary>
    public class PerformanceDataPoint
    {
        public DateTime Date { get; set; }
        public decimal Value { get; set; }
        public decimal Change { get; set; }
        public decimal ChangePercentage { get; set; }
    }

    /// <summary>
    /// نموذج التوزيع المستهدف
    /// </summary>
    public class TargetAllocation
    {
        public string Symbol { get; set; } = string.Empty;
        public decimal TargetPercentage { get; set; }
    }

    /// <summary>
    /// نموذج نتيجة إعادة التوازن
    /// </summary>
    public class RebalanceResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<RebalanceAction> Actions { get; set; } = new();
        public decimal EstimatedCost { get; set; }
        public decimal EstimatedFees { get; set; }
    }

    /// <summary>
    /// نموذج إجراء إعادة التوازن
    /// </summary>
    public class RebalanceAction
    {
        public string Symbol { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty; // BUY, SELL
        public decimal Quantity { get; set; }
        public decimal EstimatedPrice { get; set; }
        public decimal EstimatedValue { get; set; }
        public string Reason { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج تحليل المخاطر
    /// </summary>
    public class RiskAnalysis
    {
        public decimal OverallRiskScore { get; set; } // 1-10
        public string RiskLevel { get; set; } = string.Empty; // LOW, MEDIUM, HIGH
        public decimal Volatility { get; set; }
        public decimal MaxDrawdown { get; set; }
        public decimal ConcentrationRisk { get; set; }
        public decimal CorrelationRisk { get; set; }
        public List<RiskFactor> RiskFactors { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// نموذج عامل المخاطرة
    /// </summary>
    public class RiskFactor
    {
        public string Name { get; set; } = string.Empty;
        public decimal Score { get; set; } // 1-10
        public string Impact { get; set; } = string.Empty; // LOW, MEDIUM, HIGH
        public string Description { get; set; } = string.Empty;
    }
}
