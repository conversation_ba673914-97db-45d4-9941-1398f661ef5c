# Multi-stage Dockerfile for CryptoTradingBot

# Base runtime image
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Create directories for data and logs
RUN mkdir -p /app/data /app/logs && \
    chown -R app:app /app/data /app/logs

# Development stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS development
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Install development tools
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Copy project file and restore dependencies
COPY ["CryptoTradingBot.csproj", "./"]
RUN dotnet restore "CryptoTradingBot.csproj"

# Copy source code
COPY . .

# Build for development
RUN dotnet build "CryptoTradingBot.csproj" -c Debug -o /app/build

ENTRYPOINT ["dotnet", "run", "--project", "CryptoTradingBot.csproj"]

# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy project file and restore dependencies
COPY ["CryptoTradingBot.csproj", "."]
RUN dotnet restore "CryptoTradingBot.csproj"

# Copy source code
COPY . .

# Build the application
RUN dotnet build "CryptoTradingBot.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "CryptoTradingBot.csproj" \
    -c Release \
    -o /app/publish \
    /p:UseAppHost=false \
    --no-restore

# Final production image
FROM base AS final
WORKDIR /app

# Copy published application
COPY --from=publish /app/publish .

# Create non-root user
RUN groupadd -r app && useradd -r -g app app

# Set ownership
RUN chown -R app:app /app

# Switch to non-root user
USER app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

# Entry point
ENTRYPOINT ["dotnet", "CryptoTradingBot.dll"]
