# استخدام صورة .NET 9 الأساسية
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# استخدام صورة SDK للبناء
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["CryptoTradingBot.csproj", "."]
RUN dotnet restore "CryptoTradingBot.csproj"
COPY . .
WORKDIR "/src"
RUN dotnet build "CryptoTradingBot.csproj" -c Release -o /app/build

# نشر التطبيق
FROM build AS publish
RUN dotnet publish "CryptoTradingBot.csproj" -c Release -o /app/publish /p:UseAppHost=false

# الصورة النهائية
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "CryptoTradingBot.dll"]
