// تطبيق بوت تداول العملات المشفرة
class CryptoTradingApp {
    constructor() {
        this.apiBaseUrl = '/api';
        this.token = localStorage.getItem('authToken');
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkAuthentication();
    }

    setupEventListeners() {
        // تسجيل الدخول
        document.getElementById('login-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.login();
        });

        // حفظ الإعدادات
        document.getElementById('settings-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveSettings();
        });

        // إضافة API
        document.getElementById('add-api-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveApiConfig();
        });

        // تنفيذ صفقة يدوية
        document.getElementById('manual-trade-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.executeManualTrade();
        });
    }

    checkAuthentication() {
        if (this.token) {
            this.showMainContent();
            this.loadDashboard();
        } else {
            this.showLoginModal();
        }
    }

    showLoginModal() {
        const loginModal = new bootstrap.Modal(document.getElementById('login-modal'));
        loginModal.show();
    }

    showMainContent() {
        document.getElementById('main-content').style.display = 'block';
        document.getElementById('login-modal').style.display = 'none';
    }

    async login() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        try {
            const response = await fetch(`${this.apiBaseUrl}/account/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();

            if (response.ok) {
                this.token = data.token;
                localStorage.setItem('authToken', this.token);
                this.showMainContent();
                this.loadDashboard();
                this.showAlert('تم تسجيل الدخول بنجاح', 'success');
            } else {
                this.showAlert(data.message || 'خطأ في تسجيل الدخول', 'danger');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showAlert('خطأ في الاتصال بالخادم', 'danger');
        }
    }

    logout() {
        localStorage.removeItem('authToken');
        this.token = null;
        location.reload();
    }

    async loadDashboard() {
        try {
            // تحميل الإحصائيات
            await this.loadStats();
            // تحميل الأسعار الحالية
            await this.loadCurrentPrices();
            // تحميل سجل الأنشطة
            await this.loadActivityLog();
        } catch (error) {
            console.error('Dashboard loading error:', error);
        }
    }

    async loadStats() {
        // محاكاة البيانات - يمكن استبدالها بـ API حقيقي
        document.getElementById('total-balance').textContent = '$1,250.00';
        document.getElementById('total-profit').textContent = '$125.50';
        document.getElementById('active-trades').textContent = '3';
        document.getElementById('win-rate').textContent = '68%';
    }

    async loadCurrentPrices() {
        // محاكاة بيانات الأسعار
        const prices = [
            { symbol: 'BTC', name: 'Bitcoin', price: 45250.00, change: 2.5, volume: '1.2B' },
            { symbol: 'ETH', name: 'Ethereum', price: 3150.00, change: -1.2, volume: '850M' },
            { symbol: 'ADA', name: 'Cardano', price: 1.25, change: 5.8, volume: '420M' }
        ];

        const tbody = document.querySelector('#prices-table tbody');
        tbody.innerHTML = '';

        prices.forEach(coin => {
            const row = document.createElement('tr');
            const changeClass = coin.change >= 0 ? 'text-success' : 'text-danger';
            const changeIcon = coin.change >= 0 ? '↗' : '↘';
            
            row.innerHTML = `
                <td><strong>${coin.symbol}</strong><br><small>${coin.name}</small></td>
                <td>$${coin.price.toLocaleString()}</td>
                <td class="${changeClass}">${changeIcon} ${Math.abs(coin.change)}%</td>
                <td>${coin.volume}</td>
            `;
            tbody.appendChild(row);
        });
    }

    async loadActivityLog() {
        // محاكاة سجل الأنشطة
        const activities = [
            { action: 'شراء BTC', time: '10:30 AM', status: 'success' },
            { action: 'بيع ETH', time: '09:15 AM', status: 'success' },
            { action: 'تحديث الإعدادات', time: '08:45 AM', status: 'info' },
            { action: 'تنبيه سعر ADA', time: '08:20 AM', status: 'warning' }
        ];

        const activityLog = document.getElementById('activity-log');
        activityLog.innerHTML = '';

        activities.forEach(activity => {
            const item = document.createElement('div');
            item.className = `activity-item ${activity.status}`;
            item.innerHTML = `
                <div class="d-flex justify-content-between">
                    <span>${activity.action}</span>
                    <small>${activity.time}</small>
                </div>
            `;
            activityLog.appendChild(item);
        });
    }

    async loadUserSettings() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/account/settings`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const settings = await response.json();
                this.populateSettingsForm(settings);
                this.loadApiConfigs(settings.apiConfigurations);
            }
        } catch (error) {
            console.error('Settings loading error:', error);
        }
    }

    populateSettingsForm(settings) {
        document.getElementById('trading-mode').value = settings.tradingMode || 'PAPER';
        document.getElementById('max-investment').value = settings.maxTotalInvestment || 1000;
        document.getElementById('max-per-trade').value = settings.maxInvestmentPerTrade || 100;
        document.getElementById('risk-level').value = settings.riskLevel || 3;
        document.getElementById('stop-loss').value = settings.globalStopLoss || 10;
        document.getElementById('take-profit').value = settings.globalTakeProfit || 20;
        document.getElementById('auto-trading').checked = settings.autoTradingEnabled || false;
    }

    async saveSettings() {
        const settings = {
            tradingMode: document.getElementById('trading-mode').value,
            maxTotalInvestment: parseFloat(document.getElementById('max-investment').value),
            maxInvestmentPerTrade: parseFloat(document.getElementById('max-per-trade').value),
            riskLevel: parseInt(document.getElementById('risk-level').value),
            globalStopLoss: parseFloat(document.getElementById('stop-loss').value),
            globalTakeProfit: parseFloat(document.getElementById('take-profit').value),
            autoTradingEnabled: document.getElementById('auto-trading').checked
        };

        try {
            const response = await fetch(`${this.apiBaseUrl}/account/settings`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify(settings)
            });

            const data = await response.json();

            if (response.ok) {
                this.showAlert('تم حفظ الإعدادات بنجاح', 'success');
            } else {
                this.showAlert(data.message || 'خطأ في حفظ الإعدادات', 'danger');
            }
        } catch (error) {
            console.error('Settings save error:', error);
            this.showAlert('خطأ في الاتصال بالخادم', 'danger');
        }
    }

    loadApiConfigs(configs) {
        const container = document.getElementById('api-configs-list');
        container.innerHTML = '';

        if (!configs || configs.length === 0) {
            container.innerHTML = '<p class="text-muted">لا توجد تكوينات APIs محفوظة</p>';
            return;
        }

        configs.forEach(config => {
            const item = document.createElement('div');
            item.className = 'api-config-item';
            item.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="api-name">${config.apiName}</div>
                        <div class="api-status ${config.isActive ? 'active' : 'inactive'}">
                            <span class="status-indicator ${config.isActive ? 'online' : 'offline'}"></span>
                            ${config.isActive ? 'نشط' : 'غير نشط'}
                            ${config.isTestnet ? ' (شبكة اختبار)' : ''}
                        </div>
                        <small class="text-muted">API Key: ${config.apiKey}</small>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="app.testApiConnection('${config.apiName}')">
                            اختبار
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="app.deleteApiConfig('${config.apiName}')">
                            حذف
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(item);
        });
    }

    showAddApiModal() {
        const modal = new bootstrap.Modal(document.getElementById('add-api-modal'));
        modal.show();
    }

    async saveApiConfig() {
        const config = {
            apiName: document.getElementById('api-name').value,
            apiKey: document.getElementById('api-key').value,
            apiSecret: document.getElementById('api-secret').value,
            baseUrl: document.getElementById('base-url').value,
            isActive: true,
            isTestnet: document.getElementById('is-testnet').checked
        };

        try {
            const response = await fetch(`${this.apiBaseUrl}/account/api-config`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify(config)
            });

            const data = await response.json();

            if (response.ok) {
                this.showAlert('تم حفظ تكوين API بنجاح', 'success');
                bootstrap.Modal.getInstance(document.getElementById('add-api-modal')).hide();
                document.getElementById('add-api-form').reset();
                this.loadUserSettings(); // إعادة تحميل الإعدادات
            } else {
                this.showAlert(data.message || 'خطأ في حفظ تكوين API', 'danger');
            }
        } catch (error) {
            console.error('API config save error:', error);
            this.showAlert('خطأ في الاتصال بالخادم', 'danger');
        }
    }

    async deleteApiConfig(apiName) {
        if (!confirm(`هل أنت متأكد من حذف تكوين ${apiName}؟`)) {
            return;
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}/account/api-config/${apiName}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const data = await response.json();

            if (response.ok) {
                this.showAlert('تم حذف تكوين API بنجاح', 'success');
                this.loadUserSettings(); // إعادة تحميل الإعدادات
            } else {
                this.showAlert(data.message || 'خطأ في حذف تكوين API', 'danger');
            }
        } catch (error) {
            console.error('API config delete error:', error);
            this.showAlert('خطأ في الاتصال بالخادم', 'danger');
        }
    }

    async testApiConnection(apiName) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/account/test-api/${apiName}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const data = await response.json();

            if (response.ok) {
                const alertType = data.isConnected ? 'success' : 'warning';
                this.showAlert(data.message, alertType);
            } else {
                this.showAlert(data.message || 'خطأ في اختبار الاتصال', 'danger');
            }
        } catch (error) {
            console.error('API test error:', error);
            this.showAlert('خطأ في الاتصال بالخادم', 'danger');
        }
    }

    async executeManualTrade() {
        const trade = {
            currency: document.getElementById('trade-currency').value,
            type: document.getElementById('trade-type').value,
            amount: parseFloat(document.getElementById('trade-amount').value)
        };

        // محاكاة تنفيذ الصفقة
        this.showAlert(`تم تنفيذ ${trade.type === 'BUY' ? 'شراء' : 'بيع'} ${trade.currency} بقيمة $${trade.amount}`, 'success');
        document.getElementById('manual-trade-form').reset();
    }

    showSection(sectionId) {
        // إخفاء جميع الأقسام
        document.querySelectorAll('.content-section').forEach(section => {
            section.style.display = 'none';
        });

        // إظهار القسم المحدد
        document.getElementById(sectionId).style.display = 'block';

        // تحديث شريط التنقل
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[href="#${sectionId}"]`).classList.add('active');

        // تحميل البيانات حسب القسم
        if (sectionId === 'settings' || sectionId === 'api-config') {
            this.loadUserSettings();
        } else if (sectionId === 'dashboard') {
            this.loadDashboard();
        }
    }

    showAlert(message, type = 'info') {
        // إنشاء تنبيه Bootstrap
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// تهيئة التطبيق
const app = new CryptoTradingApp();

// دوال عامة للاستخدام في HTML
function showSection(sectionId) {
    app.showSection(sectionId);
}

function logout() {
    app.logout();
}

function showAddApiModal() {
    app.showAddApiModal();
}
