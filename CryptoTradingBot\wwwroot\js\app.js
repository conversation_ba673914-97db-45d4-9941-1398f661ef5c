// تطبيق بوت تداول العملات المشفرة
class CryptoTradingApp {
    constructor() {
        this.apiBaseUrl = '/api';
        this.token = localStorage.getItem('authToken');
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkAuthentication();
    }

    setupEventListeners() {
        // تسجيل الدخول
        document.getElementById('login-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.login();
        });

        // حفظ الإعدادات
        document.getElementById('settings-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveSettings();
        });

        // إضافة API
        document.getElementById('add-api-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveApiConfig();
        });

        // تنفيذ صفقة يدوية
        document.getElementById('manual-trade-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.executeManualTrade();
        });
    }

    checkAuthentication() {
        if (this.token) {
            this.showMainContent();
            this.loadDashboard();
        } else {
            this.showLoginModal();
        }
    }

    showLoginModal() {
        const loginModal = new bootstrap.Modal(document.getElementById('login-modal'));
        loginModal.show();
    }

    showMainContent() {
        document.getElementById('main-content').style.display = 'block';
        document.getElementById('login-modal').style.display = 'none';
    }

    async login() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        try {
            const response = await fetch(`${this.apiBaseUrl}/account/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();

            if (response.ok) {
                this.token = data.token;
                localStorage.setItem('authToken', this.token);
                this.showMainContent();
                this.loadDashboard();
                this.showAlert('تم تسجيل الدخول بنجاح', 'success');
            } else {
                this.showAlert(data.message || 'خطأ في تسجيل الدخول', 'danger');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showAlert('خطأ في الاتصال بالخادم', 'danger');
        }
    }

    logout() {
        localStorage.removeItem('authToken');
        this.token = null;
        location.reload();
    }

    async loadDashboard() {
        try {
            // تحميل الإحصائيات
            await this.loadStats();
            // تحميل الأسعار الحالية
            await this.loadCurrentPrices();
            // تحميل سجل الأنشطة
            await this.loadActivityLog();
        } catch (error) {
            console.error('Dashboard loading error:', error);
        }
    }

    async loadStats() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/trading/statistics`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const stats = await response.json();
                document.getElementById('total-balance').textContent = `$${stats.totalBalance.toLocaleString()}`;
                document.getElementById('total-profit').textContent = `$${stats.totalProfit.toLocaleString()}`;
                document.getElementById('active-trades').textContent = stats.activeTrades.toString();
                document.getElementById('win-rate').textContent = `${stats.winRate.toFixed(1)}%`;
            } else {
                // استخدام البيانات الوهمية في حالة الخطأ
                document.getElementById('total-balance').textContent = '$1,250.00';
                document.getElementById('total-profit').textContent = '$125.50';
                document.getElementById('active-trades').textContent = '3';
                document.getElementById('win-rate').textContent = '68%';
            }
        } catch (error) {
            console.error('Stats loading error:', error);
            // استخدام البيانات الوهمية في حالة الخطأ
            document.getElementById('total-balance').textContent = '$1,250.00';
            document.getElementById('total-profit').textContent = '$125.50';
            document.getElementById('active-trades').textContent = '3';
            document.getElementById('win-rate').textContent = '68%';
        }
    }

    async loadCurrentPrices() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/data/prices`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const currencies = await response.json();
                this.displayPrices(currencies);
            } else {
                // استخدام البيانات الوهمية في حالة الخطأ
                this.displayMockPrices();
            }
        } catch (error) {
            console.error('Prices loading error:', error);
            this.displayMockPrices();
        }
    }

    displayPrices(currencies) {
        const tbody = document.querySelector('#prices-table tbody');
        tbody.innerHTML = '';

        currencies.forEach(currency => {
            const row = document.createElement('tr');
            const changeClass = currency.priceChange24h >= 0 ? 'text-success' : 'text-danger';
            const changeIcon = currency.priceChange24h >= 0 ? '↗' : '↘';

            row.innerHTML = `
                <td><strong>${currency.symbol}</strong><br><small>${currency.name}</small></td>
                <td>$${currency.currentPrice.toLocaleString()}</td>
                <td class="${changeClass}">${changeIcon} ${Math.abs(currency.priceChange24h).toFixed(2)}%</td>
                <td>$${this.formatVolume(currency.volume24h)}</td>
            `;
            tbody.appendChild(row);
        });
    }

    displayMockPrices() {
        const prices = [
            { symbol: 'BTC', name: 'Bitcoin', currentPrice: 45250.00, priceChange24h: 2.5, volume24h: 1200000000 },
            { symbol: 'ETH', name: 'Ethereum', currentPrice: 3150.00, priceChange24h: -1.2, volume24h: 850000000 },
            { symbol: 'ADA', name: 'Cardano', currentPrice: 1.25, priceChange24h: 5.8, volume24h: 420000000 }
        ];

        this.displayPrices(prices);
    }

    formatVolume(volume) {
        if (volume >= 1000000000) {
            return (volume / 1000000000).toFixed(1) + 'B';
        } else if (volume >= 1000000) {
            return (volume / 1000000).toFixed(0) + 'M';
        } else if (volume >= 1000) {
            return (volume / 1000).toFixed(0) + 'K';
        }
        return volume.toString();
    }

    async loadActivityLog() {
        // محاكاة سجل الأنشطة
        const activities = [
            { action: 'شراء BTC', time: '10:30 AM', status: 'success' },
            { action: 'بيع ETH', time: '09:15 AM', status: 'success' },
            { action: 'تحديث الإعدادات', time: '08:45 AM', status: 'info' },
            { action: 'تنبيه سعر ADA', time: '08:20 AM', status: 'warning' }
        ];

        const activityLog = document.getElementById('activity-log');
        activityLog.innerHTML = '';

        activities.forEach(activity => {
            const item = document.createElement('div');
            item.className = `activity-item ${activity.status}`;
            item.innerHTML = `
                <div class="d-flex justify-content-between">
                    <span>${activity.action}</span>
                    <small>${activity.time}</small>
                </div>
            `;
            activityLog.appendChild(item);
        });
    }

    async loadUserSettings() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/account/settings`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const settings = await response.json();
                this.populateSettingsForm(settings);
                this.loadApiConfigs(settings.apiConfigurations);
            }
        } catch (error) {
            console.error('Settings loading error:', error);
        }
    }

    populateSettingsForm(settings) {
        document.getElementById('trading-mode').value = settings.tradingMode || 'PAPER';
        document.getElementById('max-investment').value = settings.maxTotalInvestment || 1000;
        document.getElementById('max-per-trade').value = settings.maxInvestmentPerTrade || 100;
        document.getElementById('risk-level').value = settings.riskLevel || 3;
        document.getElementById('stop-loss').value = settings.globalStopLoss || 10;
        document.getElementById('take-profit').value = settings.globalTakeProfit || 20;
        document.getElementById('auto-trading').checked = settings.autoTradingEnabled || false;
    }

    async saveSettings() {
        const settings = {
            tradingMode: document.getElementById('trading-mode').value,
            maxTotalInvestment: parseFloat(document.getElementById('max-investment').value),
            maxInvestmentPerTrade: parseFloat(document.getElementById('max-per-trade').value),
            riskLevel: parseInt(document.getElementById('risk-level').value),
            globalStopLoss: parseFloat(document.getElementById('stop-loss').value),
            globalTakeProfit: parseFloat(document.getElementById('take-profit').value),
            autoTradingEnabled: document.getElementById('auto-trading').checked
        };

        try {
            const response = await fetch(`${this.apiBaseUrl}/account/settings`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify(settings)
            });

            const data = await response.json();

            if (response.ok) {
                this.showAlert('تم حفظ الإعدادات بنجاح', 'success');
            } else {
                this.showAlert(data.message || 'خطأ في حفظ الإعدادات', 'danger');
            }
        } catch (error) {
            console.error('Settings save error:', error);
            this.showAlert('خطأ في الاتصال بالخادم', 'danger');
        }
    }

    loadApiConfigs(configs) {
        const container = document.getElementById('api-configs-list');
        container.innerHTML = '';

        if (!configs || configs.length === 0) {
            container.innerHTML = '<p class="text-muted">لا توجد تكوينات APIs محفوظة</p>';
            return;
        }

        configs.forEach(config => {
            const item = document.createElement('div');
            item.className = 'api-config-item';
            item.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="api-name">${config.apiName}</div>
                        <div class="api-status ${config.isActive ? 'active' : 'inactive'}">
                            <span class="status-indicator ${config.isActive ? 'online' : 'offline'}"></span>
                            ${config.isActive ? 'نشط' : 'غير نشط'}
                            ${config.isTestnet ? ' (شبكة اختبار)' : ''}
                        </div>
                        <small class="text-muted">API Key: ${config.apiKey}</small>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="app.testApiConnection('${config.apiName}')">
                            اختبار
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="app.deleteApiConfig('${config.apiName}')">
                            حذف
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(item);
        });
    }

    showAddApiModal() {
        const modal = new bootstrap.Modal(document.getElementById('add-api-modal'));
        modal.show();
    }

    async saveApiConfig() {
        const config = {
            apiName: document.getElementById('api-name').value,
            apiKey: document.getElementById('api-key').value,
            apiSecret: document.getElementById('api-secret').value,
            baseUrl: document.getElementById('base-url').value,
            isActive: true,
            isTestnet: document.getElementById('is-testnet').checked
        };

        try {
            const response = await fetch(`${this.apiBaseUrl}/account/api-config`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify(config)
            });

            const data = await response.json();

            if (response.ok) {
                this.showAlert('تم حفظ تكوين API بنجاح', 'success');
                bootstrap.Modal.getInstance(document.getElementById('add-api-modal')).hide();
                document.getElementById('add-api-form').reset();
                this.loadUserSettings(); // إعادة تحميل الإعدادات
            } else {
                this.showAlert(data.message || 'خطأ في حفظ تكوين API', 'danger');
            }
        } catch (error) {
            console.error('API config save error:', error);
            this.showAlert('خطأ في الاتصال بالخادم', 'danger');
        }
    }

    async deleteApiConfig(apiName) {
        if (!confirm(`هل أنت متأكد من حذف تكوين ${apiName}؟`)) {
            return;
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}/account/api-config/${apiName}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const data = await response.json();

            if (response.ok) {
                this.showAlert('تم حذف تكوين API بنجاح', 'success');
                this.loadUserSettings(); // إعادة تحميل الإعدادات
            } else {
                this.showAlert(data.message || 'خطأ في حذف تكوين API', 'danger');
            }
        } catch (error) {
            console.error('API config delete error:', error);
            this.showAlert('خطأ في الاتصال بالخادم', 'danger');
        }
    }

    async testApiConnection(apiName) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/account/test-api/${apiName}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const data = await response.json();

            if (response.ok) {
                const alertType = data.isConnected ? 'success' : 'warning';
                this.showAlert(data.message, alertType);
            } else {
                this.showAlert(data.message || 'خطأ في اختبار الاتصال', 'danger');
            }
        } catch (error) {
            console.error('API test error:', error);
            this.showAlert('خطأ في الاتصال بالخادم', 'danger');
        }
    }

    async executeManualTrade() {
        const trade = {
            symbol: document.getElementById('trade-currency').value,
            type: document.getElementById('trade-type').value,
            amount: parseFloat(document.getElementById('trade-amount').value),
            mode: 'PAPER',
            strategy: 'MANUAL',
            notes: 'Manual trade from web interface'
        };

        try {
            const response = await fetch(`${this.apiBaseUrl}/trading/execute`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify(trade)
            });

            const data = await response.json();

            if (response.ok && data.success) {
                this.showAlert(`تم تنفيذ ${trade.type === 'BUY' ? 'شراء' : 'بيع'} ${trade.symbol} بقيمة $${trade.amount}`, 'success');
                document.getElementById('manual-trade-form').reset();

                // تحديث الإحصائيات
                await this.loadTradingHistory();
                await this.loadStats();
            } else {
                this.showAlert(data.message || 'خطأ في تنفيذ الصفقة', 'danger');
            }
        } catch (error) {
            console.error('Manual trade error:', error);
            this.showAlert('خطأ في الاتصال بالخادم', 'danger');
        }
    }

    showSection(sectionId) {
        // إخفاء جميع الأقسام
        document.querySelectorAll('.content-section').forEach(section => {
            section.style.display = 'none';
        });

        // إظهار القسم المحدد
        document.getElementById(sectionId).style.display = 'block';

        // تحديث شريط التنقل
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[href="#${sectionId}"]`).classList.add('active');

        // تحميل البيانات حسب القسم
        if (sectionId === 'settings' || sectionId === 'api-config') {
            this.loadUserSettings();
        } else if (sectionId === 'dashboard') {
            this.loadDashboard();
        } else if (sectionId === 'trading') {
            this.loadTradingHistory();
        } else if (sectionId === 'analysis') {
            // تحميل التحليل الفني إذا لزم الأمر
        }
    }

    async loadTradingHistory() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/trading/history?page=1&pageSize=10`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.displayTradingHistory(data.trades);
            } else {
                this.displayMockTradingHistory();
            }
        } catch (error) {
            console.error('Trading history loading error:', error);
            this.displayMockTradingHistory();
        }
    }

    displayTradingHistory(trades) {
        const tbody = document.querySelector('#trades-table tbody');
        tbody.innerHTML = '';

        trades.forEach(trade => {
            const row = document.createElement('tr');
            const typeClass = trade.type === 'BUY' ? 'text-success' : 'text-danger';
            const statusClass = trade.status === 'COMPLETED' ? 'text-success' :
                               trade.status === 'PENDING' ? 'text-warning' : 'text-danger';

            const profitLoss = trade.profit || 0;
            const profitClass = profitLoss >= 0 ? 'text-success' : 'text-danger';

            row.innerHTML = `
                <td>${new Date(trade.createdAt).toLocaleDateString('ar-SA')}</td>
                <td>${trade.cryptoCurrency?.symbol || 'N/A'}</td>
                <td><span class="${typeClass}">${trade.type}</span></td>
                <td>${trade.amount.toFixed(4)}</td>
                <td>$${trade.price.toFixed(2)}</td>
                <td class="${profitClass}">$${profitLoss.toFixed(2)}</td>
                <td><span class="badge bg-${statusClass.replace('text-', '')}">${trade.status}</span></td>
            `;
            tbody.appendChild(row);
        });
    }

    displayMockTradingHistory() {
        const mockTrades = [
            {
                createdAt: new Date().toISOString(),
                cryptoCurrency: { symbol: 'BTC' },
                type: 'BUY',
                amount: 0.001,
                price: 45000,
                profit: 25.50,
                status: 'COMPLETED'
            },
            {
                createdAt: new Date(Date.now() - 86400000).toISOString(),
                cryptoCurrency: { symbol: 'ETH' },
                type: 'SELL',
                amount: 0.5,
                price: 3100,
                profit: -15.20,
                status: 'COMPLETED'
            }
        ];

        this.displayTradingHistory(mockTrades);
    }

    showAlert(message, type = 'info') {
        // إنشاء تنبيه Bootstrap
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    async loadTechnicalAnalysis() {
        const symbol = document.getElementById('analysis-currency').value;
        const content = document.getElementById('technical-analysis-content');

        content.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';

        try {
            const response = await fetch(`${this.apiBaseUrl}/ai/technical-analysis/${symbol}`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const analysis = await response.json();
                this.displayTechnicalAnalysis(analysis);
            } else {
                content.innerHTML = '<div class="alert alert-warning">لا يمكن تحميل التحليل الفني</div>';
            }
        } catch (error) {
            console.error('Technical analysis error:', error);
            content.innerHTML = '<div class="alert alert-danger">خطأ في تحميل التحليل</div>';
        }
    }

    displayTechnicalAnalysis(analysis) {
        const content = document.getElementById('technical-analysis-content');
        const signalClass = analysis.overallSignal === 'BUY' ? 'success' :
                           analysis.overallSignal === 'SELL' ? 'danger' : 'warning';

        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>الإشارة العامة</h6>
                    <span class="badge bg-${signalClass} fs-6">${analysis.overallSignal}</span>
                    <div class="mt-2">
                        <small>قوة الإشارة: ${(analysis.signalStrength * 100).toFixed(1)}%</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6>المؤشرات الفنية</h6>
                    <small>RSI: ${analysis.rsi.toFixed(2)}</small><br>
                    <small>SMA20: $${analysis.sma20.toFixed(2)}</small><br>
                    <small>التقلبات: ${analysis.volatility.toFixed(2)}%</small>
                </div>
            </div>
            <div class="mt-3">
                <h6>الاتجاه العام</h6>
                <p class="mb-1">${analysis.trendAnalysis.overallTrend}</p>
                <small class="text-muted">${analysis.summary}</small>
            </div>
        `;
    }

    async getSmartRecommendation() {
        const symbol = document.getElementById('analysis-currency').value;
        const content = document.getElementById('smart-recommendation-content');

        content.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';

        try {
            const response = await fetch(`${this.apiBaseUrl}/trading/smart-recommendation/${symbol}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const recommendation = await response.json();
                this.displaySmartRecommendation(recommendation);
            } else {
                content.innerHTML = '<div class="alert alert-warning">لا يمكن الحصول على التوصية</div>';
            }
        } catch (error) {
            console.error('Smart recommendation error:', error);
            content.innerHTML = '<div class="alert alert-danger">خطأ في الحصول على التوصية</div>';
        }
    }

    displaySmartRecommendation(recommendation) {
        const content = document.getElementById('smart-recommendation-content');
        const actionClass = recommendation.finalRecommendation.action === 'BUY' ? 'success' :
                           recommendation.finalRecommendation.action === 'SELL' ? 'danger' : 'warning';

        content.innerHTML = `
            <div class="text-center mb-3">
                <h4 class="text-${actionClass}">${recommendation.finalRecommendation.action}</h4>
                <p>الثقة: ${(recommendation.finalRecommendation.confidence * 100).toFixed(1)}%</p>
            </div>
            <div class="row">
                <div class="col-md-4 text-center">
                    <h6>التحليل الفني</h6>
                    <span class="badge bg-secondary">${recommendation.technicalAnalysis.overallSignal}</span>
                </div>
                <div class="col-md-4 text-center">
                    <h6>الذكاء الاصطناعي</h6>
                    <span class="badge bg-info">${recommendation.aiRecommendation.action}</span>
                </div>
                <div class="col-md-4 text-center">
                    <h6>تحليل المشاعر</h6>
                    <span class="badge bg-primary">${recommendation.sentimentAnalysis.overallSentiment}</span>
                </div>
            </div>
            <div class="mt-3">
                <small class="text-muted">${recommendation.finalRecommendation.reasoning}</small>
            </div>
        `;
    }

    async loadMarketAnalysis() {
        const content = document.getElementById('market-analysis-content');

        content.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';

        try {
            const response = await fetch(`${this.apiBaseUrl}/ai/market-analysis`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const analysis = await response.json();
                this.displayMarketAnalysis(analysis);
            } else {
                content.innerHTML = '<div class="alert alert-warning">لا يمكن تحميل تحليل السوق</div>';
            }
        } catch (error) {
            console.error('Market analysis error:', error);
            content.innerHTML = '<div class="alert alert-danger">خطأ في تحميل تحليل السوق</div>';
        }
    }

    displayMarketAnalysis(analysis) {
        const content = document.getElementById('market-analysis-content');

        let analysisHtml = `
            <div class="row mb-3">
                <div class="col-md-4 text-center">
                    <h6>إشارات الشراء</h6>
                    <span class="badge bg-success fs-5">${analysis.summary.bullishCount}</span>
                </div>
                <div class="col-md-4 text-center">
                    <h6>إشارات البيع</h6>
                    <span class="badge bg-danger fs-5">${analysis.summary.bearishCount}</span>
                </div>
                <div class="col-md-4 text-center">
                    <h6>إشارات محايدة</h6>
                    <span class="badge bg-warning fs-5">${analysis.summary.neutralCount}</span>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>العملة</th>
                            <th>السعر</th>
                            <th>التغيير</th>
                            <th>المشاعر</th>
                            <th>التوصية</th>
                            <th>الثقة</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        analysis.analysis.forEach(item => {
            const changeClass = item.priceChange24h >= 0 ? 'text-success' : 'text-danger';
            const recommendationClass = item.recommendation === 'BUY' ? 'success' :
                                       item.recommendation === 'SELL' ? 'danger' : 'warning';

            analysisHtml += `
                <tr>
                    <td><strong>${item.symbol}</strong></td>
                    <td>$${item.currentPrice.toLocaleString()}</td>
                    <td class="${changeClass}">${item.priceChange24h.toFixed(2)}%</td>
                    <td>${item.sentiment}</td>
                    <td><span class="badge bg-${recommendationClass}">${item.recommendation}</span></td>
                    <td>${(item.confidence * 100).toFixed(0)}%</td>
                </tr>
            `;
        });

        analysisHtml += `
                    </tbody>
                </table>
            </div>
        `;

        content.innerHTML = analysisHtml;
    }
}

// تهيئة التطبيق
const app = new CryptoTradingApp();

// دوال عامة للاستخدام في HTML
function showSection(sectionId) {
    app.showSection(sectionId);
}

function logout() {
    app.logout();
}

function showAddApiModal() {
    app.showAddApiModal();
}
