# 🚀 بوت تداول العملات المشفرة - CryptoTradingBot

## 📋 نظرة عامة

بوت تداول العملات المشفرة هو تطبيق ويب متقدم مبني بتقنية .NET 9 يوفر منصة شاملة لتداول العملات المشفرة باستخدام الذكاء الاصطناعي والتحليل الفني المتقدم.

## ✨ الميزات الرئيسية

### 🔐 الأمان والمصادقة
- نظام مصادقة JWT آمن
- تشفير البيانات الحساسة (API Keys)
- تسجيل جميع الأنشطة والعمليات
- حماية متقدمة للبيانات الشخصية

### 📊 جمع وتحليل البيانات
- جمع الأسعار الحية من CoinGecko API
- تحليل الأخبار والمشاعر
- تخزين البيانات التاريخية
- تحديث تلقائي للأسعار

### 🤖 الذكاء الاصطناعي
- تكامل مع DeepSeek AI
- تحليل المشاعر للأخبار
- توصيات تداول ذكية
- تحليل السوق الشامل

### 📈 التحليل الفني
- مؤشرات فنية متقدمة (RSI, MACD, SMA, EMA)
- تحليل الاتجاهات
- مستويات الدعم والمقاومة
- حساب التقلبات

### 💼 إدارة التداول
- Paper Trading (تداول وهمي)
- تنفيذ الصفقات اليدوية
- إدارة المخاطر
- تتبع الأرباح والخسائر

### 🎯 الاستراتيجيات
- استراتيجيات تداول متعددة
- اختبار الاستراتيجيات (Backtesting)
- تحسين الاستراتيجيات بالذكاء الاصطناعي

### 🌐 واجهة المستخدم
- واجهة ويب تفاعلية باللغة العربية
- لوحة تحكم شاملة
- رسوم بيانية وإحصائيات
- تصميم متجاوب

## 🛠️ التقنيات المستخدمة

### Backend
- **.NET 9** - إطار العمل الرئيسي
- **Entity Framework Core** - ORM لقاعدة البيانات
- **SQLite** - قاعدة البيانات
- **JWT** - المصادقة والتفويض
- **AutoMapper** - تحويل البيانات
- **Serilog** - تسجيل الأحداث

### Frontend
- **HTML5 & CSS3** - هيكل وتصميم الواجهة
- **Bootstrap 5** - إطار عمل CSS
- **JavaScript (ES6+)** - البرمجة التفاعلية
- **Font Awesome** - الأيقونات

### APIs الخارجية
- **CoinGecko API** - أسعار العملات المشفرة
- **NewsAPI** - الأخبار (اختياري)
- **DeepSeek AI** - الذكاء الاصطناعي

## 🚀 التثبيت والتشغيل

### المتطلبات
- .NET 9 SDK
- Visual Studio 2022 أو VS Code
- Git

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-repo/CryptoTradingBot.git
cd CryptoTradingBot
```

2. **تثبيت الحزم**
```bash
dotnet restore
```

3. **تكوين الإعدادات**
قم بتحديث `appsettings.json`:
```json
{
  "ApiSettings": {
    "DeepSeek": {
      "ApiKey": "your-deepseek-api-key"
    },
    "NewsAPI": {
      "ApiKey": "your-newsapi-key"
    }
  }
}
```

4. **تشغيل التطبيق**
```bash
dotnet run
```

5. **الوصول للتطبيق**
افتح المتصفح وانتقل إلى: `http://localhost:5168`

### بيانات الدخول الافتراضية
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

## 📁 هيكل المشروع

```
CryptoTradingBot/
├── Controllers/          # تحكم API
├── Data/                 # سياق قاعدة البيانات
├── Models/               # نماذج البيانات
├── Services/             # خدمات الأعمال
├── wwwroot/             # الملفات الثابتة
│   ├── css/             # ملفات التصميم
│   ├── js/              # ملفات JavaScript
│   └── index.html       # الواجهة الرئيسية
├── appsettings.json     # إعدادات التطبيق
└── Program.cs           # نقطة البداية
```

## 🔧 التكوين

### إعدادات قاعدة البيانات
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=cryptobot.db"
  }
}
```

### إعدادات APIs
```json
{
  "ApiSettings": {
    "DeepSeek": {
      "BaseUrl": "https://api.deepseek.com/v1",
      "Model": "deepseek-chat",
      "ApiKey": "your-api-key"
    },
    "NewsAPI": {
      "BaseUrl": "https://newsapi.org/v2",
      "ApiKey": "your-api-key"
    }
  }
}
```

## 📊 استخدام التطبيق

### 1. لوحة التحكم
- عرض الإحصائيات العامة
- مراقبة الأسعار الحية
- تتبع الأرباح والخسائر

### 2. التداول
- تنفيذ صفقات يدوية
- عرض سجل التداول
- إدارة الصفقات النشطة

### 3. التحليل الفني
- تحليل العملات المختلفة
- عرض المؤشرات الفنية
- الحصول على توصيات ذكية

### 4. الإعدادات
- تكوين APIs
- إدارة إعدادات التداول
- تخصيص المخاطر

## 🔒 الأمان

- **تشفير البيانات:** جميع API Keys مشفرة
- **مصادقة JWT:** حماية جميع endpoints
- **تسجيل الأنشطة:** تتبع جميع العمليات
- **حماية CSRF:** حماية من الهجمات

## 🧪 الاختبار

```bash
# تشغيل الاختبارات
dotnet test

# فحص التغطية
dotnet test --collect:"XPlat Code Coverage"
```

## 📈 الأداء

- **استجابة سريعة:** أقل من 200ms للطلبات العادية
- **تحديث تلقائي:** كل 30 ثانية للأسعار
- **ذاكرة محسنة:** استخدام فعال للموارد
- **قاعدة بيانات محسنة:** فهارس متقدمة

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع جديد (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

- **البريد الإلكتروني:** <EMAIL>
- **GitHub Issues:** [إنشاء مشكلة جديدة](https://github.com/your-repo/CryptoTradingBot/issues)
- **التوثيق:** [Wiki](https://github.com/your-repo/CryptoTradingBot/wiki)

## 🎯 الخطط المستقبلية

- [ ] تكامل مع منصات تداول حقيقية
- [ ] تطبيق موبايل
- [ ] المزيد من المؤشرات الفنية
- [ ] تحليل متقدم بالذكاء الاصطناعي
- [ ] إشعارات فورية
- [ ] تداول آلي متقدم

## 🏆 الإنجازات

- ✅ واجهة مستخدم متكاملة
- ✅ تكامل AI متقدم
- ✅ تحليل فني شامل
- ✅ نظام أمان قوي
- ✅ أداء محسن

---

**تم تطوير هذا المشروع بـ ❤️ لمجتمع التداول العربي**
