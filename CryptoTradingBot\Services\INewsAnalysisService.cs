using CryptoTradingBot.Models;

namespace CryptoTradingBot.Services
{
    /// <summary>
    /// واجهة خدمة تحليل الأخبار
    /// </summary>
    public interface INewsAnalysisService
    {
        /// <summary>
        /// تحليل مشاعر الأخبار
        /// </summary>
        /// <param name="newsData">بيانات الأخبار</param>
        /// <returns>نتيجة التحليل</returns>
        Task<NewsAnalysisResult> AnalyzeNewsAsync(NewsData newsData);

        /// <summary>
        /// الحصول على تحليل المشاعر العام للعملة
        /// </summary>
        /// <param name="symbol">رمز العملة</param>
        /// <param name="hours">عدد الساعات الماضية</param>
        /// <returns>تحليل المشاعر العام</returns>
        Task<OverallSentimentResult> GetOverallSentimentAsync(string symbol, int hours = 24);

        /// <summary>
        /// استخراج الكلمات المفتاحية من الأخبار
        /// </summary>
        /// <param name="content">محتوى الخبر</param>
        /// <returns>قائمة الكلمات المفتاحية</returns>
        Task<List<string>> ExtractKeywordsAsync(string content);

        /// <summary>
        /// تصنيف الأخبار حسب الفئة
        /// </summary>
        /// <param name="newsData">بيانات الأخبار</param>
        /// <returns>فئة الخبر</returns>
        Task<string> CategorizeNewsAsync(NewsData newsData);

        /// <summary>
        /// ربط الأخبار بالعملات المشفرة ذات الصلة
        /// </summary>
        /// <param name="newsData">بيانات الأخبار</param>
        /// <returns>قائمة العملات ذات الصلة مع درجة الصلة</returns>
        Task<List<(string Symbol, decimal Relevance)>> FindRelatedCurrenciesAsync(NewsData newsData);

        /// <summary>
        /// الحصول على ملخص الأخبار
        /// </summary>
        /// <param name="content">محتوى الخبر</param>
        /// <returns>ملخص الخبر</returns>
        Task<string> SummarizeNewsAsync(string content);
    }

    /// <summary>
    /// نموذج نتيجة تحليل الأخبار
    /// </summary>
    public class NewsAnalysisResult
    {
        public long NewsId { get; set; }
        public decimal SentimentScore { get; set; }
        public string SentimentLabel { get; set; } = string.Empty;
        public decimal Confidence { get; set; }
        public List<string> Keywords { get; set; } = new();
        public string Category { get; set; } = string.Empty;
        public string Summary { get; set; } = string.Empty;
        public List<(string Symbol, decimal Relevance)> RelatedCurrencies { get; set; } = new();
        public Dictionary<string, decimal> EmotionScores { get; set; } = new();
    }

    /// <summary>
    /// نموذج تحليل المشاعر العام
    /// </summary>
    public class OverallSentimentResult
    {
        public string Symbol { get; set; } = string.Empty;
        public decimal AverageSentiment { get; set; }
        public string OverallLabel { get; set; } = string.Empty;
        public int NewsCount { get; set; }
        public decimal Confidence { get; set; }
        public DateTime AnalysisTime { get; set; } = DateTime.UtcNow;
        public List<NewsImpact> TopNews { get; set; } = new();
    }

    /// <summary>
    /// نموذج تأثير الأخبار
    /// </summary>
    public class NewsImpact
    {
        public string Title { get; set; } = string.Empty;
        public decimal SentimentScore { get; set; }
        public decimal Impact { get; set; }
        public DateTime PublishedAt { get; set; }
        public string Source { get; set; } = string.Empty;
    }
}
