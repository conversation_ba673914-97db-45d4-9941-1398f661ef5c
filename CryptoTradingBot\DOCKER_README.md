# 🐳 Docker Compose Setup - CryptoTradingBot

## 📋 نظرة عامة

تم إعداد **CryptoTradingBot** للعمل مع Docker Compose مع مجموعة شاملة من الخدمات للتطوير والإنتاج.

## 🏗️ الخدمات المتاحة

### **الخدمات الأساسية:**
- **cryptotradingbot** - التطبيق الرئيسي
- **nginx** - Reverse Proxy وخادم الويب
- **redis** - التخزين المؤقت والجلسات

### **خدمات المراقبة:**
- **grafana** - لوحات المراقبة والتحليل
- **prometheus** - جمع المقاييس والمراقبة

## 🚀 البدء السريع

### **1. الإعداد الأولي:**
```bash
# إنشاء المجلدات المطلوبة
make setup

# أو يدوياً:
mkdir -p data logs nginx/logs grafana/dashboards grafana/datasources
cp .env.example .env
```

### **2. تحرير ملف البيئة:**
```bash
# تحرير ملف .env وإضافة API Keys
nano .env
```

### **3. بناء وتشغيل البيئة:**

#### **للتطوير:**
```bash
# البدء السريع
make quick-start

# أو خطوة بخطوة:
make dev-build
make dev-up
```

#### **للإنتاج:**
```bash
make prod-build
make prod-up
```

## 📊 الوصول للخدمات

### **التطوير:**
- **التطبيق الرئيسي:** http://localhost:5168
- **Nginx:** http://localhost:8080
- **Grafana:** http://localhost:3000 (admin/admin123)
- **Prometheus:** http://localhost:9091
- **Redis:** localhost:6379

### **الإنتاج:**
- **التطبيق:** http://localhost:80
- **Grafana:** http://localhost:3000
- **Prometheus:** http://localhost:9090
- **Redis:** localhost:6379

## 🛠️ الأوامر المتاحة

### **إدارة البيئة:**
```bash
make dev-up          # تشغيل بيئة التطوير
make dev-down        # إيقاف بيئة التطوير
make dev-logs        # عرض سجلات التطوير
make dev-restart     # إعادة تشغيل بيئة التطوير

make prod-up         # تشغيل بيئة الإنتاج
make prod-down       # إيقاف بيئة الإنتاج
make prod-logs       # عرض سجلات الإنتاج
```

### **إدارة البيانات:**
```bash
make db-backup       # نسخ احتياطي لقاعدة البيانات
make db-restore BACKUP_FILE=filename  # استعادة قاعدة البيانات
```

### **المراقبة:**
```bash
make monitor         # فتح لوحات المراقبة
make health          # فحص صحة الخدمات
make status          # عرض حالة الخدمات
```

### **الصيانة:**
```bash
make clean           # تنظيف الموارد
make clean-all       # تنظيف شامل
make update          # تحديث الصور
```

## 📁 هيكل الملفات

```
CryptoTradingBot/
├── docker-compose.yml              # الإعداد الأساسي
├── docker-compose.override.yml     # إعدادات التطوير
├── docker-compose.prod.yml         # إعدادات الإنتاج
├── Dockerfile                      # ملف بناء التطبيق
├── .dockerignore                   # ملفات مستبعدة من البناء
├── .env                           # متغيرات البيئة
├── .env.example                   # مثال لمتغيرات البيئة
├── Makefile                       # أوامر الإدارة
├── nginx/
│   ├── nginx.conf                 # إعدادات Nginx
│   └── logs/                      # سجلات Nginx
├── redis/
│   └── redis.conf                 # إعدادات Redis
├── prometheus/
│   └── prometheus.yml             # إعدادات Prometheus
├── grafana/
│   ├── dashboards/                # لوحات Grafana
│   └── datasources/               # مصادر البيانات
├── data/                          # قاعدة البيانات
└── logs/                          # سجلات التطبيق
```

## ⚙️ التكوين

### **متغيرات البيئة المهمة:**
```env
# API Keys
DEEPSEEK_API_KEY=your_api_key
NEWSAPI_KEY=your_api_key
COINGECKO_API_KEY=your_api_key

# Security
JWT_SECRET_KEY=your_secret_key
ENCRYPTION_KEY=your_encryption_key

# Performance
REDIS_MAX_MEMORY=256mb
APP_MAX_MEMORY=1G
```

### **إعدادات الشبكة:**
- **الشبكة:** crypto-network (172.20.0.0/16)
- **DNS:** خدمات Docker الداخلية

## 🔧 استكشاف الأخطاء

### **مشاكل شائعة:**

#### **1. فشل في بناء التطبيق:**
```bash
# تنظيف وإعادة البناء
make clean
make dev-build
```

#### **2. مشاكل في الاتصال:**
```bash
# فحص حالة الخدمات
make status
make health
```

#### **3. مشاكل في قاعدة البيانات:**
```bash
# فحص سجلات التطبيق
make app-logs
```

#### **4. مشاكل في Redis:**
```bash
# فحص Redis
make redis-cli
# داخل Redis CLI:
ping
info
```

### **سجلات مفيدة:**
```bash
# سجلات التطبيق
make app-logs

# سجلات Nginx
make nginx-logs

# سجلات جميع الخدمات
make logs
```

## 🔒 الأمان

### **إعدادات الأمان:**
- تشغيل التطبيق بمستخدم غير root
- تشفير البيانات الحساسة
- فصل الشبكات
- Health checks للخدمات

### **أفضل الممارسات:**
1. تغيير كلمات المرور الافتراضية
2. استخدام HTTPS في الإنتاج
3. تحديث الصور بانتظام
4. مراقبة السجلات

## 📈 المراقبة

### **Grafana Dashboards:**
- أداء التطبيق
- استخدام الذاكرة والمعالج
- معدلات الطلبات
- أخطاء التطبيق

### **Prometheus Metrics:**
- مقاييس .NET
- مقاييس Redis
- مقاييس Nginx
- مقاييس مخصصة للتداول

## 🚀 النشر

### **للإنتاج:**
1. تحديث ملف `.env` بالقيم الصحيحة
2. تشغيل `make prod-build`
3. تشغيل `make prod-up`
4. مراقبة السجلات `make prod-logs`

### **التحديثات:**
```bash
# تحديث الصور
make update

# إعادة بناء وتشغيل
make prod-build
make prod-up
```

---

**للمساعدة:** `make help`  
**للدعم:** راجع السجلات أو افتح issue في المشروع
