using CryptoTradingBot.Models;

namespace CryptoTradingBot.Services
{
    /// <summary>
    /// واجهة خدمة التداول
    /// </summary>
    public interface ITradingService
    {
        /// <summary>
        /// تنفيذ صفقة تداول
        /// </summary>
        /// <param name="operation">تفاصيل العملية</param>
        /// <returns>نتيجة التنفيذ</returns>
        Task<TradingResult> ExecuteTradeAsync(TradingOperation operation);

        /// <summary>
        /// الحصول على الصفقات النشطة
        /// </summary>
        /// <returns>قائمة الصفقات النشطة</returns>
        Task<List<TradingOperation>> GetActiveTradesAsync();

        /// <summary>
        /// إلغاء صفقة
        /// </summary>
        /// <param name="operationId">معرف العملية</param>
        /// <returns>نتيجة الإلغاء</returns>
        Task<bool> CancelTradeAsync(long operationId);

        /// <summary>
        /// حساب الربح/الخسارة للصفقة
        /// </summary>
        /// <param name="operation">العملية</param>
        /// <param name="currentPrice">السعر الحالي</param>
        /// <returns>الربح/الخسارة</returns>
        decimal CalculateProfitLoss(TradingOperation operation, decimal currentPrice);

        /// <summary>
        /// التحقق من شروط Stop Loss و Take Profit
        /// </summary>
        /// <param name="operation">العملية</param>
        /// <param name="currentPrice">السعر الحالي</param>
        /// <returns>true إذا كان يجب إغلاق الصفقة</returns>
        Task<bool> ShouldCloseTrade(TradingOperation operation, decimal currentPrice);

        /// <summary>
        /// الحصول على إحصائيات التداول
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>إحصائيات التداول</returns>
        Task<TradingStatistics> GetTradingStatisticsAsync(string userId);

        /// <summary>
        /// تحديث أسعار الصفقات النشطة
        /// </summary>
        Task UpdateActiveTradesAsync();
    }

    /// <summary>
    /// نموذج نتيجة التداول
    /// </summary>
    public class TradingResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public long? OperationId { get; set; }
        public decimal? ExecutedPrice { get; set; }
        public decimal? Fee { get; set; }
        public DateTime? ExecutionTime { get; set; }
    }

    /// <summary>
    /// نموذج إحصائيات التداول
    /// </summary>
    public class TradingStatistics
    {
        public decimal TotalBalance { get; set; }
        public decimal TotalProfit { get; set; }
        public decimal TotalLoss { get; set; }
        public int TotalTrades { get; set; }
        public int WinningTrades { get; set; }
        public int LosingTrades { get; set; }
        public int ActiveTrades { get; set; }
        public decimal WinRate { get; set; }
        public decimal AverageProfit { get; set; }
        public decimal AverageLoss { get; set; }
        public decimal MaxDrawdown { get; set; }
        public decimal SharpeRatio { get; set; }
    }
}
