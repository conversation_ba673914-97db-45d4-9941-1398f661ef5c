using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CryptoTradingBot.Models
{
    /// <summary>
    /// نموذج البيانات التاريخية للأسعار
    /// </summary>
    public class PriceHistory
    {
        [Key]
        public long Id { get; set; }

        [Required]
        public int CryptoCurrencyId { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal Price { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Volume { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal High { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal Low { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal Open { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal Close { get; set; }

        public DateTime Timestamp { get; set; }

        [StringLength(20)]
        public string TimeFrame { get; set; } = "1h"; // 1m, 5m, 15m, 1h, 4h, 1d

        [StringLength(50)]
        public string Source { get; set; } = string.Empty; // CoinGecko, Binance, etc.

        // العلاقات
        [ForeignKey("CryptoCurrencyId")]
        public virtual CryptoCurrency CryptoCurrency { get; set; } = null!;
    }
}
