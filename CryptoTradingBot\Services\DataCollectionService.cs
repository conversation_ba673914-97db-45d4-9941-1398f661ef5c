using CryptoTradingBot.Models;
using CryptoTradingBot.Data;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace CryptoTradingBot.Services
{
    /// <summary>
    /// خدمة جمع البيانات من APIs المختلفة
    /// </summary>
    public class DataCollectionService : IDataCollectionService
    {
        private readonly CryptoTradingDbContext _context;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;
        private readonly ILogger<DataCollectionService> _logger;

        public DataCollectionService(
            CryptoTradingDbContext context,
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration,
            ILogger<DataCollectionService> logger)
        {
            _context = context;
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<List<CryptoCurrency>> GetCurrentPricesAsync(List<string> symbols)
        {
            var currencies = new List<CryptoCurrency>();

            try
            {
                // محاكاة جلب البيانات من CoinGecko API
                // في التطبيق الحقيقي، سيتم استخدام API حقيقي
                var mockData = new Dictionary<string, (decimal price, decimal change, decimal volume)>
                {
                    { "BTC", (45250.00m, 2.5m, 1200000000m) },
                    { "ETH", (3150.00m, -1.2m, 850000000m) },
                    { "ADA", (1.25m, 5.8m, 420000000m) },
                    { "DOT", (35.50m, 3.2m, 180000000m) },
                    { "SOL", (125.75m, -0.8m, 320000000m) }
                };

                foreach (var symbol in symbols)
                {
                    if (mockData.ContainsKey(symbol))
                    {
                        var data = mockData[symbol];
                        var currency = await _context.CryptoCurrencies
                            .FirstOrDefaultAsync(c => c.Symbol == symbol);

                        if (currency != null)
                        {
                            currency.CurrentPrice = data.price;
                            currency.PriceChange24h = data.change;
                            currency.Volume24h = data.volume;
                            currency.UpdatedAt = DateTime.UtcNow;
                            currencies.Add(currency);
                        }
                    }
                }

                _logger.LogInformation($"Retrieved current prices for {currencies.Count} currencies");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving current prices");
            }

            return currencies;
        }

        public async Task<List<PriceHistory>> GetHistoricalPricesAsync(string symbol, int days, string interval = "1h")
        {
            var priceHistories = new List<PriceHistory>();

            try
            {
                var currency = await _context.CryptoCurrencies
                    .FirstOrDefaultAsync(c => c.Symbol == symbol);

                if (currency == null)
                {
                    _logger.LogWarning($"Currency {symbol} not found");
                    return priceHistories;
                }

                // محاكاة البيانات التاريخية
                var random = new Random();
                var basePrice = 45000m; // سعر أساسي للمحاكاة
                var startDate = DateTime.UtcNow.AddDays(-days);

                for (int i = 0; i < days * 24; i++) // ساعة واحدة لكل نقطة
                {
                    var timestamp = startDate.AddHours(i);
                    var priceVariation = (decimal)(random.NextDouble() * 0.1 - 0.05); // تغيير ±5%
                    var price = basePrice * (1 + priceVariation);

                    var priceHistory = new PriceHistory
                    {
                        CryptoCurrencyId = currency.Id,
                        Price = price,
                        High = price * 1.02m,
                        Low = price * 0.98m,
                        Open = price * 0.999m,
                        Close = price,
                        Volume = random.Next(1000000, 10000000),
                        Timestamp = timestamp,
                        TimeFrame = interval,
                        Source = "MockData"
                    };

                    priceHistories.Add(priceHistory);
                    basePrice = price; // استخدام السعر الحالي كأساس للنقطة التالية
                }

                _logger.LogInformation($"Generated {priceHistories.Count} historical price points for {symbol}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving historical prices for {symbol}");
            }

            return priceHistories;
        }

        public async Task<List<NewsData>> GetCryptoNewsAsync(List<string> keywords, int hours = 24)
        {
            var newsData = new List<NewsData>();

            try
            {
                // محاكاة أخبار العملات المشفرة
                var mockNews = new[]
                {
                    new NewsData
                    {
                        Title = "Bitcoin reaches new monthly high amid institutional adoption",
                        Content = "Bitcoin price surged to new monthly highs as major institutions continue to adopt cryptocurrency...",
                        Summary = "Bitcoin price increases due to institutional adoption",
                        Source = "CryptoNews",
                        Author = "John Doe",
                        PublishedAt = DateTime.UtcNow.AddHours(-2),
                        SentimentScore = 0.8m,
                        SentimentLabel = "POSITIVE",
                        Confidence = 0.9m,
                        Keywords = "Bitcoin,BTC,institutional,adoption",
                        Category = "Market"
                    },
                    new NewsData
                    {
                        Title = "Ethereum network upgrade shows promising results",
                        Content = "The latest Ethereum network upgrade has shown significant improvements in transaction speed...",
                        Summary = "Ethereum upgrade improves network performance",
                        Source = "EthereumDaily",
                        Author = "Jane Smith",
                        PublishedAt = DateTime.UtcNow.AddHours(-4),
                        SentimentScore = 0.6m,
                        SentimentLabel = "POSITIVE",
                        Confidence = 0.8m,
                        Keywords = "Ethereum,ETH,upgrade,network",
                        Category = "Technology"
                    },
                    new NewsData
                    {
                        Title = "Regulatory concerns impact cryptocurrency market",
                        Content = "New regulatory discussions have created uncertainty in the cryptocurrency market...",
                        Summary = "Regulatory uncertainty affects crypto market",
                        Source = "RegulationWatch",
                        Author = "Mike Johnson",
                        PublishedAt = DateTime.UtcNow.AddHours(-6),
                        SentimentScore = -0.4m,
                        SentimentLabel = "NEGATIVE",
                        Confidence = 0.7m,
                        Keywords = "regulation,government,uncertainty",
                        Category = "Regulation"
                    }
                };

                newsData.AddRange(mockNews);
                _logger.LogInformation($"Retrieved {newsData.Count} news articles");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving crypto news");
            }

            return newsData;
        }

        public async Task UpdateCurrenciesAsync(List<CryptoCurrency> currencies)
        {
            try
            {
                foreach (var currency in currencies)
                {
                    _context.CryptoCurrencies.Update(currency);
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation($"Updated {currencies.Count} currencies");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating currencies");
                throw;
            }
        }

        public async Task SavePriceHistoriesAsync(List<PriceHistory> priceHistories)
        {
            try
            {
                // تجنب الازدواجية
                foreach (var priceHistory in priceHistories)
                {
                    var existing = await _context.PriceHistories
                        .FirstOrDefaultAsync(p => 
                            p.CryptoCurrencyId == priceHistory.CryptoCurrencyId &&
                            p.Timestamp == priceHistory.Timestamp &&
                            p.TimeFrame == priceHistory.TimeFrame);

                    if (existing == null)
                    {
                        _context.PriceHistories.Add(priceHistory);
                    }
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation($"Saved {priceHistories.Count} price history records");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving price histories");
                throw;
            }
        }

        public async Task SaveNewsDataAsync(List<NewsData> newsData)
        {
            try
            {
                foreach (var news in newsData)
                {
                    var existing = await _context.NewsData
                        .FirstOrDefaultAsync(n => n.Title == news.Title && n.PublishedAt == news.PublishedAt);

                    if (existing == null)
                    {
                        _context.NewsData.Add(news);
                    }
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation($"Saved {newsData.Count} news articles");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving news data");
                throw;
            }
        }

        public async Task<Dictionary<string, bool>> CheckApiStatusAsync()
        {
            var status = new Dictionary<string, bool>();

            try
            {
                // فحص حالة APIs المختلفة
                status["CoinGecko"] = await CheckCoinGeckoStatus();
                status["AlphaVantage"] = await CheckAlphaVantageStatus();
                status["DeepSeek"] = await CheckDeepSeekStatus();

                _logger.LogInformation("API status check completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking API status");
            }

            return status;
        }

        private async Task<bool> CheckCoinGeckoStatus()
        {
            try
            {
                using var client = _httpClientFactory.CreateClient();
                var response = await client.GetAsync("https://api.coingecko.com/api/v3/ping");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> CheckAlphaVantageStatus()
        {
            // محاكاة فحص Alpha Vantage
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> CheckDeepSeekStatus()
        {
            // محاكاة فحص DeepSeek
            await Task.Delay(100);
            return true;
        }
    }
}
