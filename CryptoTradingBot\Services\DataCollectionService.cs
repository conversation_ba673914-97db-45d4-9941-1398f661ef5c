using CryptoTradingBot.Models;
using CryptoTradingBot.Data;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace CryptoTradingBot.Services
{
    /// <summary>
    /// خدمة جمع البيانات من APIs المختلفة
    /// </summary>
    public class DataCollectionService : IDataCollectionService
    {
        private readonly CryptoTradingDbContext _context;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;
        private readonly ILogger<DataCollectionService> _logger;

        public DataCollectionService(
            CryptoTradingDbContext context,
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration,
            ILogger<DataCollectionService> logger)
        {
            _context = context;
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<List<CryptoCurrency>> GetCurrentPricesAsync(List<string> symbols)
        {
            var currencies = new List<CryptoCurrency>();

            try
            {
                // محاولة جلب البيانات من CoinGecko API الحقيقي
                var realData = await GetRealPricesFromCoinGecko(symbols);

                if (realData.Any())
                {
                    foreach (var symbol in symbols)
                    {
                        if (realData.ContainsKey(symbol))
                        {
                            var data = realData[symbol];
                            var currency = await _context.CryptoCurrencies
                                .FirstOrDefaultAsync(c => c.Symbol == symbol);

                            if (currency != null)
                            {
                                currency.CurrentPrice = data.price;
                                currency.PriceChange24h = data.change;
                                currency.Volume24h = data.volume;
                                currency.MarketCap = data.marketCap;
                                currency.UpdatedAt = DateTime.UtcNow;
                                currencies.Add(currency);
                            }
                        }
                    }
                }
                else
                {
                    // استخدام البيانات الوهمية كبديل
                    currencies = await GetMockPricesData(symbols);
                }

                _logger.LogInformation($"Retrieved current prices for {currencies.Count} currencies");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving current prices");
                // استخدام البيانات الوهمية في حالة الخطأ
                currencies = await GetMockPricesData(symbols);
            }

            return currencies;
        }

        public async Task<List<PriceHistory>> GetHistoricalPricesAsync(string symbol, int days, string interval = "1h")
        {
            var priceHistories = new List<PriceHistory>();

            try
            {
                var currency = await _context.CryptoCurrencies
                    .FirstOrDefaultAsync(c => c.Symbol == symbol);

                if (currency == null)
                {
                    _logger.LogWarning($"Currency {symbol} not found");
                    return priceHistories;
                }

                // محاكاة البيانات التاريخية
                var random = new Random();
                var basePrice = 45000m; // سعر أساسي للمحاكاة
                var startDate = DateTime.UtcNow.AddDays(-days);

                for (int i = 0; i < days * 24; i++) // ساعة واحدة لكل نقطة
                {
                    var timestamp = startDate.AddHours(i);
                    var priceVariation = (decimal)(random.NextDouble() * 0.1 - 0.05); // تغيير ±5%
                    var price = basePrice * (1 + priceVariation);

                    var priceHistory = new PriceHistory
                    {
                        CryptoCurrencyId = currency.Id,
                        Price = price,
                        High = price * 1.02m,
                        Low = price * 0.98m,
                        Open = price * 0.999m,
                        Close = price,
                        Volume = random.Next(1000000, 10000000),
                        Timestamp = timestamp,
                        TimeFrame = interval,
                        Source = "MockData"
                    };

                    priceHistories.Add(priceHistory);
                    basePrice = price; // استخدام السعر الحالي كأساس للنقطة التالية
                }

                _logger.LogInformation($"Generated {priceHistories.Count} historical price points for {symbol}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving historical prices for {symbol}");
            }

            return priceHistories;
        }

        public async Task<List<NewsData>> GetCryptoNewsAsync(List<string> keywords, int hours = 24)
        {
            var newsData = new List<NewsData>();

            try
            {
                // محاولة جلب الأخبار الحقيقية أولاً
                var realNews = await GetRealNewsData(keywords, hours);

                if (realNews.Any())
                {
                    newsData.AddRange(realNews);
                }
                else
                {
                    // استخدام الأخبار الوهمية كبديل
                    newsData.AddRange(GetMockNewsData());
                }

                _logger.LogInformation($"Retrieved {newsData.Count} news articles");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving crypto news");
                // استخدام الأخبار الوهمية في حالة الخطأ
                newsData.AddRange(GetMockNewsData());
            }

            return newsData;
        }

        public async Task UpdateCurrenciesAsync(List<CryptoCurrency> currencies)
        {
            try
            {
                foreach (var currency in currencies)
                {
                    _context.CryptoCurrencies.Update(currency);
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation($"Updated {currencies.Count} currencies");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating currencies");
                throw;
            }
        }

        public async Task SavePriceHistoriesAsync(List<PriceHistory> priceHistories)
        {
            try
            {
                // تجنب الازدواجية
                foreach (var priceHistory in priceHistories)
                {
                    var existing = await _context.PriceHistories
                        .FirstOrDefaultAsync(p => 
                            p.CryptoCurrencyId == priceHistory.CryptoCurrencyId &&
                            p.Timestamp == priceHistory.Timestamp &&
                            p.TimeFrame == priceHistory.TimeFrame);

                    if (existing == null)
                    {
                        _context.PriceHistories.Add(priceHistory);
                    }
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation($"Saved {priceHistories.Count} price history records");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving price histories");
                throw;
            }
        }

        public async Task SaveNewsDataAsync(List<NewsData> newsData)
        {
            try
            {
                foreach (var news in newsData)
                {
                    var existing = await _context.NewsData
                        .FirstOrDefaultAsync(n => n.Title == news.Title && n.PublishedAt == news.PublishedAt);

                    if (existing == null)
                    {
                        _context.NewsData.Add(news);
                    }
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation($"Saved {newsData.Count} news articles");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving news data");
                throw;
            }
        }

        public async Task<Dictionary<string, bool>> CheckApiStatusAsync()
        {
            var status = new Dictionary<string, bool>();

            try
            {
                // فحص حالة APIs المختلفة
                status["CoinGecko"] = await CheckCoinGeckoStatus();
                status["AlphaVantage"] = await CheckAlphaVantageStatus();
                status["DeepSeek"] = await CheckDeepSeekStatus();

                _logger.LogInformation("API status check completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking API status");
            }

            return status;
        }

        private async Task<bool> CheckCoinGeckoStatus()
        {
            try
            {
                using var client = _httpClientFactory.CreateClient();
                var response = await client.GetAsync("https://api.coingecko.com/api/v3/ping");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> CheckAlphaVantageStatus()
        {
            // محاكاة فحص Alpha Vantage
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> CheckDeepSeekStatus()
        {
            // محاكاة فحص DeepSeek
            await Task.Delay(100);
            return true;
        }

        private async Task<Dictionary<string, (decimal price, decimal change, decimal volume, decimal marketCap)>> GetRealPricesFromCoinGecko(List<string> symbols)
        {
            var result = new Dictionary<string, (decimal price, decimal change, decimal volume, decimal marketCap)>();

            try
            {
                using var client = _httpClientFactory.CreateClient();
                client.Timeout = TimeSpan.FromSeconds(10);

                var symbolsString = string.Join(",", symbols.Select(s => GetCoinGeckoId(s)));
                var url = $"https://api.coingecko.com/api/v3/simple/price?ids={symbolsString}&vs_currencies=usd&include_24hr_change=true&include_24hr_vol=true&include_market_cap=true";

                var response = await client.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    var data = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, decimal>>>(jsonContent);

                    if (data != null)
                    {
                        foreach (var symbol in symbols)
                        {
                            var coinId = GetCoinGeckoId(symbol);
                            if (data.ContainsKey(coinId))
                            {
                                var coinData = data[coinId];
                                result[symbol] = (
                                    price: coinData.GetValueOrDefault("usd", 0),
                                    change: coinData.GetValueOrDefault("usd_24h_change", 0),
                                    volume: coinData.GetValueOrDefault("usd_24h_vol", 0),
                                    marketCap: coinData.GetValueOrDefault("usd_market_cap", 0)
                                );
                            }
                        }
                    }

                    _logger.LogInformation($"Successfully fetched real price data from CoinGecko for {result.Count} currencies");
                }
                else
                {
                    _logger.LogWarning($"CoinGecko API returned status code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching real price data from CoinGecko");
            }

            return result;
        }

        private async Task<List<CryptoCurrency>> GetMockPricesData(List<string> symbols)
        {
            var currencies = new List<CryptoCurrency>();
            var random = new Random();

            // بيانات وهمية محسنة مع تغييرات عشوائية
            var basePrices = new Dictionary<string, decimal>
            {
                { "BTC", 45250.00m },
                { "ETH", 3150.00m },
                { "ADA", 1.25m },
                { "DOT", 35.50m },
                { "SOL", 125.75m }
            };

            foreach (var symbol in symbols)
            {
                if (basePrices.ContainsKey(symbol))
                {
                    var currency = await _context.CryptoCurrencies
                        .FirstOrDefaultAsync(c => c.Symbol == symbol);

                    if (currency != null)
                    {
                        var basePrice = basePrices[symbol];
                        var priceVariation = (decimal)(random.NextDouble() * 0.1 - 0.05); // ±5%
                        var changeVariation = (decimal)(random.NextDouble() * 10 - 5); // ±5%

                        currency.CurrentPrice = basePrice * (1 + priceVariation);
                        currency.PriceChange24h = changeVariation;
                        currency.Volume24h = random.Next(100000000, 2000000000);
                        currency.MarketCap = currency.CurrentPrice * random.Next(10000000, 100000000);
                        currency.UpdatedAt = DateTime.UtcNow;
                        currencies.Add(currency);
                    }
                }
            }

            return currencies;
        }

        private string GetCoinGeckoId(string symbol)
        {
            return symbol.ToLower() switch
            {
                "btc" => "bitcoin",
                "eth" => "ethereum",
                "ada" => "cardano",
                "dot" => "polkadot",
                "sol" => "solana",
                _ => symbol.ToLower()
            };
        }

        private async Task<List<NewsData>> GetRealNewsData(List<string> keywords, int hours)
        {
            var newsData = new List<NewsData>();

            try
            {
                // محاولة استخدام NewsAPI أو مصدر أخبار مجاني آخر
                using var client = _httpClientFactory.CreateClient();
                client.Timeout = TimeSpan.FromSeconds(15);

                // يمكن استخدام NewsAPI أو مصادر أخرى مجانية
                // هنا مثال لـ NewsAPI (يتطلب API key)
                var apiKey = _configuration["ApiSettings:NewsAPI:ApiKey"];

                if (!string.IsNullOrEmpty(apiKey))
                {
                    var keywordsString = string.Join(" OR ", keywords);
                    var fromDate = DateTime.UtcNow.AddHours(-hours).ToString("yyyy-MM-dd");
                    var url = $"https://newsapi.org/v2/everything?q={keywordsString}&from={fromDate}&sortBy=publishedAt&apiKey={apiKey}";

                    var response = await client.GetAsync(url);

                    if (response.IsSuccessStatusCode)
                    {
                        var jsonContent = await response.Content.ReadAsStringAsync();
                        var newsResponse = JsonSerializer.Deserialize<NewsApiResponse>(jsonContent);

                        if (newsResponse?.Articles != null)
                        {
                            foreach (var article in newsResponse.Articles.Take(10)) // أخذ أول 10 أخبار
                            {
                                var news = new NewsData
                                {
                                    Title = article.Title ?? "No Title",
                                    Content = article.Content,
                                    Summary = article.Description,
                                    Url = article.Url,
                                    Source = article.Source?.Name ?? "Unknown",
                                    Author = article.Author,
                                    PublishedAt = article.PublishedAt ?? DateTime.UtcNow,
                                    SentimentScore = 0, // سيتم تحليله لاحقاً
                                    SentimentLabel = "NEUTRAL",
                                    Confidence = 0.5m,
                                    Category = "General",
                                    IsProcessed = false
                                };

                                newsData.Add(news);
                            }

                            _logger.LogInformation($"Successfully fetched {newsData.Count} real news articles");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching real news data");
            }

            return newsData;
        }

        private List<NewsData> GetMockNewsData()
        {
            var random = new Random();
            var mockTitles = new[]
            {
                "Bitcoin reaches new monthly high amid institutional adoption",
                "Ethereum network upgrade shows promising results",
                "Regulatory concerns impact cryptocurrency market",
                "Major exchange announces new security features",
                "DeFi protocol launches innovative yield farming",
                "Central bank discusses digital currency plans",
                "Cryptocurrency adoption grows in emerging markets",
                "New blockchain technology promises faster transactions"
            };

            var mockSources = new[] { "CryptoNews", "BlockchainDaily", "CoinTelegraph", "CryptoSlate" };
            var mockAuthors = new[] { "John Doe", "Jane Smith", "Mike Johnson", "Sarah Wilson" };

            return mockTitles.Select(title => new NewsData
            {
                Title = title,
                Content = $"This is the content for: {title}. Lorem ipsum dolor sit amet, consectetur adipiscing elit...",
                Summary = $"Summary of {title}",
                Source = mockSources[random.Next(mockSources.Length)],
                Author = mockAuthors[random.Next(mockAuthors.Length)],
                PublishedAt = DateTime.UtcNow.AddHours(-random.Next(1, 24)),
                SentimentScore = (decimal)(random.NextDouble() * 2 - 1), // -1 to 1
                SentimentLabel = random.Next(3) switch { 0 => "POSITIVE", 1 => "NEGATIVE", _ => "NEUTRAL" },
                Confidence = (decimal)(0.5 + random.NextDouble() * 0.4), // 0.5 to 0.9
                Keywords = "cryptocurrency,bitcoin,blockchain",
                Category = random.Next(4) switch { 0 => "Market", 1 => "Technology", 2 => "Regulation", _ => "General" },
                IsProcessed = false
            }).ToList();
        }

        // نماذج للاستجابة من NewsAPI
        private class NewsApiResponse
        {
            public string Status { get; set; } = string.Empty;
            public int TotalResults { get; set; }
            public List<NewsArticle> Articles { get; set; } = new();
        }

        private class NewsArticle
        {
            public NewsSource? Source { get; set; }
            public string? Author { get; set; }
            public string? Title { get; set; }
            public string? Description { get; set; }
            public string? Url { get; set; }
            public string? UrlToImage { get; set; }
            public DateTime? PublishedAt { get; set; }
            public string? Content { get; set; }
        }

        private class NewsSource
        {
            public string? Id { get; set; }
            public string? Name { get; set; }
        }
    }
}
