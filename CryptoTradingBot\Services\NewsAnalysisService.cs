using CryptoTradingBot.Models;
using CryptoTradingBot.Data;
using Microsoft.EntityFrameworkCore;
using System.Text.RegularExpressions;

namespace CryptoTradingBot.Services
{
    /// <summary>
    /// خدمة تحليل الأخبار والمشاعر
    /// </summary>
    public class NewsAnalysisService : INewsAnalysisService
    {
        private readonly CryptoTradingDbContext _context;
        private readonly IDeepSeekService _deepSeekService;
        private readonly ILogger<NewsAnalysisService> _logger;

        // قاموس العملات المشفرة والكلمات المرتبطة بها
        private readonly Dictionary<string, List<string>> _currencyKeywords = new()
        {
            { "BTC", new List<string> { "bitcoin", "btc", "satoshi", "digital gold" } },
            { "ETH", new List<string> { "ethereum", "eth", "ether", "smart contract", "defi", "vitalik" } },
            { "ADA", new List<string> { "cardano", "ada", "hoskinson", "ouroboros" } },
            { "DOT", new List<string> { "polkadot", "dot", "parachain", "kusama" } },
            { "SOL", new List<string> { "solana", "sol", "proof of history", "anatoly" } }
        };

        // كلمات إيجابية وسلبية للتحليل الأساسي
        private readonly List<string> _positiveWords = new()
        {
            "bullish", "surge", "rally", "gain", "profit", "adoption", "breakthrough",
            "partnership", "upgrade", "innovation", "growth", "success", "positive"
        };

        private readonly List<string> _negativeWords = new()
        {
            "bearish", "crash", "dump", "loss", "decline", "regulation", "ban",
            "hack", "scam", "fraud", "negative", "concern", "risk", "fall"
        };

        public NewsAnalysisService(
            CryptoTradingDbContext context,
            IDeepSeekService deepSeekService,
            ILogger<NewsAnalysisService> logger)
        {
            _context = context;
            _deepSeekService = deepSeekService;
            _logger = logger;
        }

        public async Task<NewsAnalysisResult> AnalyzeNewsAsync(NewsData newsData)
        {
            try
            {
                var result = new NewsAnalysisResult
                {
                    NewsId = newsData.Id
                };

                // تحليل المشاعر باستخدام DeepSeek
                var sentimentResult = await _deepSeekService.AnalyzeSentimentAsync(newsData.Content ?? newsData.Title);
                result.SentimentScore = sentimentResult.Score;
                result.SentimentLabel = sentimentResult.Label;
                result.Confidence = sentimentResult.Confidence;
                result.EmotionScores = sentimentResult.Emotions;

                // استخراج الكلمات المفتاحية
                result.Keywords = await ExtractKeywordsAsync(newsData.Content ?? newsData.Title);

                // تصنيف الأخبار
                result.Category = await CategorizeNewsAsync(newsData);

                // إنشاء ملخص
                result.Summary = await SummarizeNewsAsync(newsData.Content ?? newsData.Title);

                // العثور على العملات ذات الصلة
                result.RelatedCurrencies = await FindRelatedCurrenciesAsync(newsData);

                // تحديث بيانات الأخبار في قاعدة البيانات
                newsData.SentimentScore = result.SentimentScore;
                newsData.SentimentLabel = result.SentimentLabel;
                newsData.Confidence = result.Confidence;
                newsData.Keywords = string.Join(",", result.Keywords);
                newsData.Category = result.Category;
                newsData.Summary = result.Summary ?? string.Empty;
                newsData.IsProcessed = true;

                _context.NewsData.Update(newsData);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Analyzed news: {newsData.Title} - Sentiment: {result.SentimentLabel} ({result.SentimentScore:F2})");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error analyzing news {newsData.Id}");
                return new NewsAnalysisResult { NewsId = newsData.Id };
            }
        }

        public async Task<OverallSentimentResult> GetOverallSentimentAsync(string symbol, int hours = 24)
        {
            try
            {
                var cutoffTime = DateTime.UtcNow.AddHours(-hours);
                
                // الحصول على الأخبار المرتبطة بالعملة
                var relatedNews = await _context.NewsData
                    .Where(n => n.PublishedAt >= cutoffTime && 
                               n.IsProcessed && 
                               (n.Keywords.Contains(symbol.ToLower()) || 
                                n.Title.ToLower().Contains(symbol.ToLower()) ||
                                n.Content.ToLower().Contains(symbol.ToLower())))
                    .OrderByDescending(n => n.PublishedAt)
                    .ToListAsync();

                if (!relatedNews.Any())
                {
                    return new OverallSentimentResult
                    {
                        Symbol = symbol,
                        OverallLabel = "NEUTRAL",
                        NewsCount = 0
                    };
                }

                // حساب المتوسط المرجح للمشاعر
                var totalWeight = 0m;
                var weightedSentiment = 0m;

                foreach (var news in relatedNews)
                {
                    // وزن أكبر للأخبار الأحدث
                    var hoursOld = (decimal)(DateTime.UtcNow - news.PublishedAt).TotalHours;
                    var timeWeight = Math.Max(0.1m, 1m - (hoursOld / (hours * 2))); // تقليل الوزن مع الوقت
                    var confidenceWeight = news.Confidence;
                    var totalNewsWeight = timeWeight * confidenceWeight;

                    weightedSentiment += news.SentimentScore * totalNewsWeight;
                    totalWeight += totalNewsWeight;
                }

                var averageSentiment = totalWeight > 0 ? weightedSentiment / totalWeight : 0;
                var overallLabel = averageSentiment > 0.1m ? "POSITIVE" : 
                                  (averageSentiment < -0.1m ? "NEGATIVE" : "NEUTRAL");

                // أهم الأخبار المؤثرة
                var topNews = relatedNews
                    .Take(5)
                    .Select(n => new NewsImpact
                    {
                        Title = n.Title,
                        SentimentScore = n.SentimentScore,
                        Impact = Math.Abs(n.SentimentScore) * n.Confidence,
                        PublishedAt = n.PublishedAt,
                        Source = n.Source ?? "Unknown"
                    })
                    .OrderByDescending(n => n.Impact)
                    .ToList();

                return new OverallSentimentResult
                {
                    Symbol = symbol,
                    AverageSentiment = averageSentiment,
                    OverallLabel = overallLabel,
                    NewsCount = relatedNews.Count,
                    Confidence = totalWeight / relatedNews.Count,
                    TopNews = topNews
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting overall sentiment for {symbol}");
                return new OverallSentimentResult { Symbol = symbol };
            }
        }

        public async Task<List<string>> ExtractKeywordsAsync(string content)
        {
            try
            {
                if (string.IsNullOrEmpty(content))
                    return new List<string>();

                var keywords = new List<string>();
                var contentLower = content.ToLower();

                // استخراج أسماء العملات المشفرة
                foreach (var currency in _currencyKeywords)
                {
                    foreach (var keyword in currency.Value)
                    {
                        if (contentLower.Contains(keyword))
                        {
                            keywords.Add(currency.Key);
                            break;
                        }
                    }
                }

                // استخراج كلمات مفتاحية أخرى
                var technicalTerms = new[]
                {
                    "blockchain", "defi", "nft", "mining", "staking", "yield",
                    "protocol", "dapp", "wallet", "exchange", "trading",
                    "market cap", "volume", "price", "bull", "bear"
                };

                foreach (var term in technicalTerms)
                {
                    if (contentLower.Contains(term) && !keywords.Contains(term))
                    {
                        keywords.Add(term);
                    }
                }

                // إزالة المكررات وإرجاع أهم 10 كلمات
                return keywords.Distinct().Take(10).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting keywords");
                return new List<string>();
            }
        }

        public async Task<string> CategorizeNewsAsync(NewsData newsData)
        {
            try
            {
                var content = (newsData.Content ?? newsData.Title).ToLower();

                // قواعد التصنيف البسيطة
                if (content.Contains("regulation") || content.Contains("government") || content.Contains("legal"))
                    return "Regulation";

                if (content.Contains("technology") || content.Contains("upgrade") || content.Contains("protocol"))
                    return "Technology";

                if (content.Contains("partnership") || content.Contains("adoption") || content.Contains("institutional"))
                    return "Adoption";

                if (content.Contains("price") || content.Contains("trading") || content.Contains("market"))
                    return "Market";

                if (content.Contains("hack") || content.Contains("security") || content.Contains("breach"))
                    return "Security";

                return "General";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error categorizing news");
                return "General";
            }
        }

        public async Task<List<(string Symbol, decimal Relevance)>> FindRelatedCurrenciesAsync(NewsData newsData)
        {
            try
            {
                var relatedCurrencies = new List<(string Symbol, decimal Relevance)>();
                var content = (newsData.Content ?? newsData.Title).ToLower();

                foreach (var currency in _currencyKeywords)
                {
                    var relevance = 0m;
                    var mentions = 0;

                    foreach (var keyword in currency.Value)
                    {
                        var regex = new Regex($@"\b{Regex.Escape(keyword)}\b", RegexOptions.IgnoreCase);
                        var matches = regex.Matches(content);
                        mentions += matches.Count;
                    }

                    if (mentions > 0)
                    {
                        // حساب درجة الصلة بناءً على عدد المرات المذكورة وموقعها
                        relevance = Math.Min(1.0m, mentions * 0.3m);
                        
                        // زيادة الصلة إذا ذُكرت في العنوان
                        if (newsData.Title.ToLower().Contains(currency.Key.ToLower()))
                        {
                            relevance += 0.3m;
                        }

                        relatedCurrencies.Add((currency.Key, Math.Min(1.0m, relevance)));
                    }
                }

                return relatedCurrencies.OrderByDescending(c => c.Relevance).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding related currencies");
                return new List<(string Symbol, decimal Relevance)>();
            }
        }

        public async Task<string> SummarizeNewsAsync(string content)
        {
            try
            {
                if (string.IsNullOrEmpty(content) || content.Length <= 200)
                    return content;

                // ملخص بسيط - أخذ أول جملتين
                var sentences = content.Split('.', StringSplitOptions.RemoveEmptyEntries);
                if (sentences.Length >= 2)
                {
                    return string.Join(". ", sentences.Take(2)) + ".";
                }

                // إذا كان النص قصير، إرجاع أول 200 حرف
                return content.Length > 200 ? content.Substring(0, 200) + "..." : content;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error summarizing news");
                return content.Length > 200 ? content.Substring(0, 200) + "..." : content;
            }
        }
    }
}
