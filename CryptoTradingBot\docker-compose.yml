version: '3.8'

services:
  # التطبيق الرئيسي
  cryptotradingbot:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: crypto-trading-bot
    ports:
      - "8080:80"
      - "8443:443"
    depends_on:
      - redis
      - nginx
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Data Source=/app/data/cryptobot.db
      - ConnectionStrings__Redis=redis:6379
      - ApiSettings__DeepSeek__ApiKey=${DEEPSEEK_API_KEY:-}
      - ApiSettings__NewsAPI__ApiKey=${NEWSAPI_KEY:-}
      - ASPNETCORE_URLS=http://+:80
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - crypto-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: crypto-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    networks:
      - crypto-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis للتخزين المؤقت
  redis:
    image: redis:7-alpine
    container_name: crypto-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - crypto-network
    restart: unless-stopped
    command: redis-server /usr/local/etc/redis/redis.conf --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Grafana للمراقبة والتحليل
  grafana:
    image: grafana/grafana:latest
    container_name: crypto-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - crypto-network
    restart: unless-stopped

  # Prometheus للمراقبة
  prometheus:
    image: prom/prometheus:latest
    container_name: crypto-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - crypto-network
    restart: unless-stopped

volumes:
  redis_data:
    driver: local
  grafana_data:
    driver: local
  prometheus_data:
    driver: local

networks:
  crypto-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
