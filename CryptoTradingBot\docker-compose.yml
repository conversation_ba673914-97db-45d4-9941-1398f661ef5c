version: '3.8'

services:
  # التطبيق الرئيسي
  cryptotradingbot:
    build: .
    container_name: crypto-trading-bot
    ports:
      - "8080:80"
      - "8443:443"
    depends_on:
      - postgres
      - redis
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=cryptobot;Username=admin;Password=CryptoBot123!
      - ConnectionStrings__Redis=redis:6379
    networks:
      - crypto-network
    restart: unless-stopped

  # قاعدة بيانات PostgreSQL
  postgres:
    image: postgres:15
    container_name: crypto-postgres
    environment:
      POSTGRES_DB: cryptobot
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: CryptoBot123!
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - crypto-network
    restart: unless-stopped

  # Redis للتخزين المؤقت
  redis:
    image: redis:7-alpine
    container_name: crypto-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - crypto-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # pgAdmin لإدارة قاعدة البيانات
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: crypto-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "8081:80"
    depends_on:
      - postgres
    networks:
      - crypto-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  crypto-network:
    driver: bridge
