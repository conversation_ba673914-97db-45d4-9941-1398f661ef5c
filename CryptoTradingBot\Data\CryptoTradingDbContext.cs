using Microsoft.EntityFrameworkCore;
using CryptoTradingBot.Models;

namespace CryptoTradingBot.Data
{
    /// <summary>
    /// سياق قاعدة البيانات الرئيسي
    /// </summary>
    public class CryptoTradingDbContext : DbContext
    {
        public CryptoTradingDbContext(DbContextOptions<CryptoTradingDbContext> options) : base(options)
        {
        }

        // الجداول الرئيسية
        public DbSet<CryptoCurrency> CryptoCurrencies { get; set; }
        public DbSet<PriceHistory> PriceHistories { get; set; }
        public DbSet<TradingOperation> TradingOperations { get; set; }
        public DbSet<NewsData> NewsData { get; set; }
        public DbSet<NewsCryptoCurrency> NewsCryptoCurrencies { get; set; }
        public DbSet<TradingStrategy> TradingStrategies { get; set; }
        public DbSet<StrategyBacktest> StrategyBacktests { get; set; }
        public DbSet<UserSettings> UserSettings { get; set; }
        public DbSet<ApiConfiguration> ApiConfigurations { get; set; }
        public DbSet<ActivityLog> ActivityLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين العلاقات والفهارس
            ConfigureIndexes(modelBuilder);
            ConfigureRelationships(modelBuilder);
            SeedInitialData(modelBuilder);
        }

        private void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // فهارس للأداء
            modelBuilder.Entity<CryptoCurrency>()
                .HasIndex(c => c.Symbol)
                .IsUnique();

            modelBuilder.Entity<PriceHistory>()
                .HasIndex(p => new { p.CryptoCurrencyId, p.Timestamp, p.TimeFrame });

            modelBuilder.Entity<TradingOperation>()
                .HasIndex(t => new { t.CryptoCurrencyId, t.CreatedAt });

            modelBuilder.Entity<TradingOperation>()
                .HasIndex(t => t.Status);

            modelBuilder.Entity<NewsData>()
                .HasIndex(n => n.PublishedAt);

            modelBuilder.Entity<NewsData>()
                .HasIndex(n => n.SentimentScore);

            modelBuilder.Entity<ActivityLog>()
                .HasIndex(a => a.CreatedAt);

            modelBuilder.Entity<ApiConfiguration>()
                .HasIndex(a => new { a.UserSettingsId, a.ApiName })
                .IsUnique();
        }

        private void ConfigureRelationships(ModelBuilder modelBuilder)
        {
            // علاقة CryptoCurrency مع PriceHistory
            modelBuilder.Entity<PriceHistory>()
                .HasOne(p => p.CryptoCurrency)
                .WithMany(c => c.PriceHistories)
                .HasForeignKey(p => p.CryptoCurrencyId)
                .OnDelete(DeleteBehavior.Cascade);

            // علاقة CryptoCurrency مع TradingOperation
            modelBuilder.Entity<TradingOperation>()
                .HasOne(t => t.CryptoCurrency)
                .WithMany(c => c.TradingOperations)
                .HasForeignKey(t => t.CryptoCurrencyId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقة NewsData مع NewsCryptoCurrency
            modelBuilder.Entity<NewsCryptoCurrency>()
                .HasOne(nc => nc.NewsData)
                .WithMany(n => n.NewsCryptoCurrencies)
                .HasForeignKey(nc => nc.NewsDataId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<NewsCryptoCurrency>()
                .HasOne(nc => nc.CryptoCurrency)
                .WithMany()
                .HasForeignKey(nc => nc.CryptoCurrencyId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقة TradingStrategy مع StrategyBacktest
            modelBuilder.Entity<StrategyBacktest>()
                .HasOne(sb => sb.TradingStrategy)
                .WithMany(ts => ts.StrategyBacktests)
                .HasForeignKey(sb => sb.TradingStrategyId)
                .OnDelete(DeleteBehavior.Cascade);

            // علاقة UserSettings مع ApiConfiguration
            modelBuilder.Entity<ApiConfiguration>()
                .HasOne(ac => ac.UserSettings)
                .WithMany(us => us.ApiConfigurations)
                .HasForeignKey(ac => ac.UserSettingsId)
                .OnDelete(DeleteBehavior.Cascade);
        }

        private void SeedInitialData(ModelBuilder modelBuilder)
        {
            // بيانات أولية للعملات المشفرة الشائعة
            modelBuilder.Entity<CryptoCurrency>().HasData(
                new CryptoCurrency { Id = 1, Symbol = "BTC", Name = "Bitcoin", Description = "The first cryptocurrency" },
                new CryptoCurrency { Id = 2, Symbol = "ETH", Name = "Ethereum", Description = "Smart contract platform" },
                new CryptoCurrency { Id = 3, Symbol = "ADA", Name = "Cardano", Description = "Proof-of-stake blockchain" },
                new CryptoCurrency { Id = 4, Symbol = "DOT", Name = "Polkadot", Description = "Multi-chain protocol" },
                new CryptoCurrency { Id = 5, Symbol = "SOL", Name = "Solana", Description = "High-performance blockchain" }
            );

            // إعدادات افتراضية
            modelBuilder.Entity<UserSettings>().HasData(
                new UserSettings 
                { 
                    Id = 1, 
                    UserId = "default",
                    TradingMode = "PAPER",
                    MaxTotalInvestment = 1000m,
                    MaxInvestmentPerTrade = 100m,
                    RiskLevel = 3m,
                    GlobalStopLoss = 10m,
                    GlobalTakeProfit = 20m,
                    PreferredCurrencies = "BTC,ETH,ADA"
                }
            );

            // استراتيجيات افتراضية
            modelBuilder.Entity<TradingStrategy>().HasData(
                new TradingStrategy
                {
                    Id = 1,
                    Name = "Simple Moving Average",
                    Description = "Basic SMA crossover strategy",
                    Type = "TECHNICAL",
                    Parameters = "{\"short_period\": 10, \"long_period\": 20}",
                    MinConfidence = 0.6m,
                    MaxInvestmentPerTrade = 50m,
                    StopLossPercentage = 5m,
                    TakeProfitPercentage = 10m,
                    IsActive = true,
                    Priority = 1
                },
                new TradingStrategy
                {
                    Id = 2,
                    Name = "Sentiment Analysis",
                    Description = "News sentiment based trading",
                    Type = "SENTIMENT",
                    Parameters = "{\"sentiment_threshold\": 0.7, \"news_lookback_hours\": 24}",
                    MinConfidence = 0.7m,
                    MaxInvestmentPerTrade = 75m,
                    StopLossPercentage = 7m,
                    TakeProfitPercentage = 15m,
                    IsActive = true,
                    Priority = 2
                }
            );
        }
    }
}
