{"format": 1, "restore": {"\\\\tsclient\\C\\Users\\mahfu\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\CryptoTradingBot.csproj": {}}, "projects": {"\\\\tsclient\\C\\Users\\mahfu\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\CryptoTradingBot.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "\\\\tsclient\\C\\Users\\mahfu\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\CryptoTradingBot.csproj", "projectName": "CryptoTradingBot", "projectPath": "\\\\tsclient\\C\\Users\\mahfu\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\CryptoTradingBot.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "\\\\tsclient\\C\\Users\\mahfu\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Ater.DeepSeek.Core": {"target": "Package", "version": "[1.1.5, )"}, "CCXT.NET": {"target": "Package", "version": "[1.5.1, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.7, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.41, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[9.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}