using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CryptoTradingBot.Models
{
    /// <summary>
    /// نموذج استراتيجيات التداول
    /// </summary>
    public class TradingStrategy
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(50)]
        public string Type { get; set; } = string.Empty; // TECHNICAL, SENTIMENT, AI, HYBRID

        [Column(TypeName = "text")]
        public string Parameters { get; set; } = "{}"; // JSON parameters

        [Column(TypeName = "decimal(5,2)")]
        public decimal MinConfidence { get; set; } = 0.7m; // Minimum confidence to execute

        [Column(TypeName = "decimal(18,8)")]
        public decimal MaxInvestmentPerTrade { get; set; } = 100m;

        [Column(TypeName = "decimal(5,2)")]
        public decimal StopLossPercentage { get; set; } = 5m;

        [Column(TypeName = "decimal(5,2)")]
        public decimal TakeProfitPercentage { get; set; } = 10m;

        public bool IsActive { get; set; } = true;

        public int Priority { get; set; } = 1; // Higher number = higher priority

        // إحصائيات الأداء
        public int TotalTrades { get; set; } = 0;
        public int WinningTrades { get; set; } = 0;
        public int LosingTrades { get; set; } = 0;

        [Column(TypeName = "decimal(18,8)")]
        public decimal TotalProfit { get; set; } = 0m;

        [Column(TypeName = "decimal(5,2)")]
        public decimal WinRate { get; set; } = 0m;

        [Column(TypeName = "decimal(5,2)")]
        public decimal AverageReturn { get; set; } = 0m;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        [StringLength(100)]
        public string? CreatedBy { get; set; }

        // العلاقات
        public virtual ICollection<StrategyBacktest> StrategyBacktests { get; set; } = new List<StrategyBacktest>();
    }

    /// <summary>
    /// نموذج نتائج اختبار الاستراتيجيات
    /// </summary>
    public class StrategyBacktest
    {
        [Key]
        public long Id { get; set; }

        public int TradingStrategyId { get; set; }

        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal InitialBalance { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal FinalBalance { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal TotalReturn { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal ReturnPercentage { get; set; }

        public int TotalTrades { get; set; }
        public int WinningTrades { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal WinRate { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal MaxDrawdown { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal SharpeRatio { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column(TypeName = "text")]
        public string? DetailedResults { get; set; } // JSON with detailed results

        // العلاقات
        [ForeignKey("TradingStrategyId")]
        public virtual TradingStrategy TradingStrategy { get; set; } = null!;
    }
}
