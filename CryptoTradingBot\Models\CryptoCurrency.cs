using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CryptoTradingBot.Models
{
    /// <summary>
    /// نموذج العملة المشفرة
    /// </summary>
    public class CryptoCurrency
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(10)]
        public string Symbol { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal CurrentPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal MarketCap { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Volume24h { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal PriceChange24h { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // العلاقات
        public virtual ICollection<PriceHistory> PriceHistories { get; set; } = new List<PriceHistory>();
        public virtual ICollection<TradingOperation> TradingOperations { get; set; } = new List<TradingOperation>();
    }
}
