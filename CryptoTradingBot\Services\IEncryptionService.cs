namespace CryptoTradingBot.Services
{
    /// <summary>
    /// واجهة خدمة التشفير
    /// </summary>
    public interface IEncryptionService
    {
        /// <summary>
        /// تشفير النص
        /// </summary>
        /// <param name="plainText">النص المراد تشفيره</param>
        /// <returns>النص المشفر</returns>
        string Encrypt(string plainText);

        /// <summary>
        /// فك تشفير النص
        /// </summary>
        /// <param name="cipherText">النص المشفر</param>
        /// <returns>النص الأصلي</returns>
        string Decrypt(string cipherText);

        /// <summary>
        /// إنشاء hash آمن للكلمة السرية
        /// </summary>
        /// <param name="password">كلمة السر</param>
        /// <returns>Hash آمن</returns>
        string HashPassword(string password);

        /// <summary>
        /// التحقق من كلمة السر
        /// </summary>
        /// <param name="password">كلمة السر</param>
        /// <param name="hash">الـ hash المحفوظ</param>
        /// <returns>true إذا كانت صحيحة</returns>
        bool VerifyPassword(string password, string hash);

        /// <summary>
        /// إنشاء JWT token
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="email">البريد الإلكتروني</param>
        /// <returns>JWT token</returns>
        string GenerateJwtToken(string userId, string email);
    }
}
