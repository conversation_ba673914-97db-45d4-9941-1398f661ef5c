using CryptoTradingBot.Data;
using CryptoTradingBot.Models;
using Microsoft.EntityFrameworkCore;

namespace CryptoTradingBot.Services
{
    /// <summary>
    /// خدمة خلفية للتداول التلقائي
    /// </summary>
    public class TradingBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<TradingBackgroundService> _logger;
        private readonly IConfiguration _configuration;

        public TradingBackgroundService(
            IServiceProvider serviceProvider,
            ILogger<TradingBackgroundService> logger,
            IConfiguration configuration)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _configuration = configuration;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Trading Background Service started");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await ProcessTradingCycle();
                    
                    // انتظار دقيقة واحدة قبل الدورة التالية
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("Trading Background Service is stopping");
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in Trading Background Service");
                    
                    // انتظار 30 ثانية في حالة الخطأ
                    await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
                }
            }
        }

        private async Task ProcessTradingCycle()
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<CryptoTradingDbContext>();
            var tradingService = scope.ServiceProvider.GetRequiredService<ITradingService>();
            var deepSeekService = scope.ServiceProvider.GetRequiredService<IDeepSeekService>();
            var newsAnalysisService = scope.ServiceProvider.GetRequiredService<INewsAnalysisService>();

            try
            {
                // التحقق من إعدادات التداول التلقائي
                var userSettings = await context.UserSettings
                    .FirstOrDefaultAsync(s => s.UserId == "default");

                if (userSettings == null || !userSettings.AutoTradingEnabled)
                {
                    return; // التداول التلقائي غير مفعل
                }

                _logger.LogInformation("Starting trading cycle");

                // تحديث الصفقات النشطة
                await tradingService.UpdateActiveTradesAsync();

                // تحليل الفرص التجارية الجديدة
                await AnalyzeTradingOpportunities(context, tradingService, deepSeekService, newsAnalysisService, userSettings);

                _logger.LogInformation("Trading cycle completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during trading cycle");
            }
        }

        private async Task AnalyzeTradingOpportunities(
            CryptoTradingDbContext context,
            ITradingService tradingService,
            IDeepSeekService deepSeekService,
            INewsAnalysisService newsAnalysisService,
            UserSettings userSettings)
        {
            try
            {
                // الحصول على العملات المفضلة
                var preferredCurrencies = userSettings.PreferredCurrencies?.Split(',') ?? new[] { "BTC", "ETH", "ADA" };
                
                var currencies = await context.CryptoCurrencies
                    .Where(c => c.IsActive && preferredCurrencies.Contains(c.Symbol))
                    .ToListAsync();

                // التحقق من عدد الصفقات النشطة
                var activeTrades = await tradingService.GetActiveTradesAsync();
                if (activeTrades.Count >= userSettings.MaxConcurrentTrades)
                {
                    _logger.LogInformation($"Maximum concurrent trades reached: {activeTrades.Count}");
                    return;
                }

                foreach (var currency in currencies)
                {
                    try
                    {
                        await AnalyzeCurrencyOpportunity(
                            context, 
                            tradingService, 
                            deepSeekService, 
                            newsAnalysisService, 
                            currency, 
                            userSettings);

                        // تأخير قصير بين التحليلات
                        await Task.Delay(2000);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Error analyzing {currency.Symbol}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error analyzing trading opportunities");
            }
        }

        private async Task AnalyzeCurrencyOpportunity(
            CryptoTradingDbContext context,
            ITradingService tradingService,
            IDeepSeekService deepSeekService,
            INewsAnalysisService newsAnalysisService,
            CryptoCurrency currency,
            UserSettings userSettings)
        {
            try
            {
                // جمع البيانات للتحليل
                var priceHistory = await context.PriceHistories
                    .Where(p => p.CryptoCurrencyId == currency.Id)
                    .OrderByDescending(p => p.Timestamp)
                    .Take(100)
                    .ToListAsync();

                if (!priceHistory.Any())
                {
                    _logger.LogWarning($"No price history found for {currency.Symbol}");
                    return;
                }

                // تحليل المشاعر العام
                var sentimentResult = await newsAnalysisService.GetOverallSentimentAsync(currency.Symbol, 24);

                // الحصول على توصية من DeepSeek
                var recommendation = await deepSeekService.AnalyzeAndRecommendAsync(
                    currency.Symbol, 
                    priceHistory, 
                    sentimentResult);

                // تقييم التوصية
                if (ShouldExecuteRecommendation(recommendation, userSettings, sentimentResult))
                {
                    await ExecuteRecommendedTrade(tradingService, currency, recommendation, userSettings);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error analyzing opportunity for {currency.Symbol}");
            }
        }

        private bool ShouldExecuteRecommendation(
            TradingRecommendation recommendation,
            UserSettings userSettings,
            OverallSentimentResult sentimentResult)
        {
            // التحقق من مستوى الثقة
            if (recommendation.Confidence < 0.7m)
            {
                _logger.LogInformation($"Low confidence for {recommendation.Symbol}: {recommendation.Confidence:F2}");
                return false;
            }

            // التحقق من توافق المشاعر مع التوصية
            if (recommendation.Action == "BUY" && sentimentResult.AverageSentiment < -0.3m)
            {
                _logger.LogInformation($"Negative sentiment conflicts with BUY recommendation for {recommendation.Symbol}");
                return false;
            }

            if (recommendation.Action == "SELL" && sentimentResult.AverageSentiment > 0.3m)
            {
                _logger.LogInformation($"Positive sentiment conflicts with SELL recommendation for {recommendation.Symbol}");
                return false;
            }

            // التحقق من مستوى المخاطرة
            var riskAdjustedConfidence = recommendation.Confidence * (userSettings.RiskLevel / 5m);
            if (riskAdjustedConfidence < 0.6m)
            {
                _logger.LogInformation($"Risk-adjusted confidence too low for {recommendation.Symbol}: {riskAdjustedConfidence:F2}");
                return false;
            }

            return recommendation.Action != "HOLD";
        }

        private async Task ExecuteRecommendedTrade(
            ITradingService tradingService,
            CryptoCurrency currency,
            TradingRecommendation recommendation,
            UserSettings userSettings)
        {
            try
            {
                var tradeAmount = Math.Min(
                    recommendation.SuggestedAmount,
                    userSettings.MaxInvestmentPerTrade);

                var operation = new TradingOperation
                {
                    CryptoCurrencyId = currency.Id,
                    Type = recommendation.Action,
                    Amount = tradeAmount / currency.CurrentPrice, // تحويل إلى كمية العملة
                    Mode = userSettings.TradingMode,
                    Strategy = "AI_RECOMMENDATION",
                    Notes = $"AI Recommendation - Confidence: {recommendation.Confidence:F2}, Reasoning: {recommendation.Reasoning}"
                };

                var result = await tradingService.ExecuteTradeAsync(operation);

                if (result.Success)
                {
                    _logger.LogInformation($"Executed {recommendation.Action} trade for {currency.Symbol}: ${tradeAmount:F2} - {result.Message}");
                }
                else
                {
                    _logger.LogWarning($"Failed to execute {recommendation.Action} trade for {currency.Symbol}: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error executing recommended trade for {currency.Symbol}");
            }
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Trading Background Service is stopping");
            await base.StopAsync(stoppingToken);
        }
    }
}
