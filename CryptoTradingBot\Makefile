# Makefile for CryptoTradingBot Docker Management

# Variables
COMPOSE_FILE = docker-compose.yml
COMPOSE_DEV_FILE = docker-compose.override.yml
COMPOSE_PROD_FILE = docker-compose.prod.yml
PROJECT_NAME = cryptotradingbot

# Default target
.DEFAULT_GOAL := help

# Help target
help: ## Show this help message
	@echo "CryptoTradingBot Docker Management"
	@echo "=================================="
	@echo ""
	@echo "Available commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development commands
dev-build: ## Build development environment
	docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_DEV_FILE) build

dev-up: ## Start development environment
	docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_DEV_FILE) up -d

dev-down: ## Stop development environment
	docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_DEV_FILE) down

dev-logs: ## Show development logs
	docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_DEV_FILE) logs -f

dev-restart: ## Restart development environment
	docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_DEV_FILE) restart

# Production commands
prod-build: ## Build production environment
	docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_PROD_FILE) build

prod-up: ## Start production environment
	docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_PROD_FILE) up -d

prod-down: ## Stop production environment
	docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_PROD_FILE) down

prod-logs: ## Show production logs
	docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_PROD_FILE) logs -f

# General commands
build: ## Build all services
	docker-compose build

up: ## Start all services
	docker-compose up -d

down: ## Stop all services
	docker-compose down

restart: ## Restart all services
	docker-compose restart

logs: ## Show logs for all services
	docker-compose logs -f

status: ## Show status of all services
	docker-compose ps

# Individual service commands
app-logs: ## Show application logs
	docker-compose logs -f cryptotradingbot

redis-logs: ## Show Redis logs
	docker-compose logs -f redis

nginx-logs: ## Show Nginx logs
	docker-compose logs -f nginx

grafana-logs: ## Show Grafana logs
	docker-compose logs -f grafana

# Database commands
db-backup: ## Backup SQLite database
	docker-compose exec cryptotradingbot cp /app/data/cryptobot.db /app/data/cryptobot_backup_$(shell date +%Y%m%d_%H%M%S).db

db-restore: ## Restore SQLite database (specify BACKUP_FILE)
	@if [ -z "$(BACKUP_FILE)" ]; then echo "Please specify BACKUP_FILE=filename"; exit 1; fi
	docker-compose exec cryptotradingbot cp /app/data/$(BACKUP_FILE) /app/data/cryptobot.db

# Maintenance commands
clean: ## Clean up Docker resources
	docker-compose down -v --remove-orphans
	docker system prune -f

clean-all: ## Clean up all Docker resources (including images)
	docker-compose down -v --remove-orphans
	docker system prune -af

update: ## Update all images
	docker-compose pull
	docker-compose build --pull

# Monitoring commands
monitor: ## Open monitoring dashboard
	@echo "Opening monitoring dashboards..."
	@echo "Grafana: http://localhost:3000 (admin/admin123)"
	@echo "Prometheus: http://localhost:9090"

health: ## Check health of all services
	@echo "Checking service health..."
	@docker-compose ps
	@echo ""
	@echo "Application health:"
	@curl -s http://localhost:8080/health || echo "Application not responding"

# Development helpers
shell: ## Open shell in application container
	docker-compose exec cryptotradingbot /bin/bash

redis-cli: ## Open Redis CLI
	docker-compose exec redis redis-cli

# Setup commands
setup: ## Initial setup (create directories and copy env file)
	@echo "Setting up CryptoTradingBot..."
	@mkdir -p data logs nginx/logs grafana/dashboards grafana/datasources
	@if [ ! -f .env ]; then cp .env.example .env; echo "Created .env file from example"; fi
	@echo "Setup complete! Please edit .env file with your API keys."

init: setup build ## Initialize project (setup + build)
	@echo "Project initialized successfully!"

# Quick start
quick-start: setup dev-build dev-up ## Quick start for development
	@echo "Development environment started!"
	@echo "Application: http://localhost:5168"
	@echo "Grafana: http://localhost:3000"
	@echo "Prometheus: http://localhost:9091"

.PHONY: help dev-build dev-up dev-down dev-logs dev-restart prod-build prod-up prod-down prod-logs build up down restart logs status app-logs redis-logs nginx-logs grafana-logs db-backup db-restore clean clean-all update monitor health shell redis-cli setup init quick-start
