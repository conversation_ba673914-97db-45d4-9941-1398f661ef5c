# .dockerignore for CryptoTradingBot

# Build outputs
bin/
obj/
out/
publish/

# Dependencies
node_modules/
packages/

# IDE files
.vs/
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini

# Git
.git/
.gitignore
.gitattributes

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# Environment files
.env
.env.local
.env.development
.env.production

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Database files (exclude from image)
*.db
*.db-shm
*.db-wal

# Temporary files
tmp/
temp/

# Documentation
README.md
*.md
docs/

# Test files
test/
tests/
**/*.test.js
**/*.spec.js

# Configuration files that shouldn't be in image
nginx/
prometheus/
grafana/
redis/

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar
*.tar.gz
*.rar

# Large media files
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm
