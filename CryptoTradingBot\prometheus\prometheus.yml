# Prometheus Configuration for CryptoTradingBot
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # CryptoTradingBot Application
  - job_name: 'cryptotradingbot'
    static_configs:
      - targets: ['cryptotradingbot:80']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Redis Metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Nginx Metrics (if nginx-prometheus-exporter is used)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Node Exporter (if added)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

# Alerting
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Remote Write (optional)
# remote_write:
#   - url: "http://remote-storage:9201/write"

# Remote Read (optional)
# remote_read:
#   - url: "http://remote-storage:9201/read"
