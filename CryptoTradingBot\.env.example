# مثال على ملف المتغيرات البيئية
# انسخ هذا الملف إلى .env وقم بتعديل القيم

# إعدادات التطبيق
ASPNETCORE_ENVIRONMENT=Production
ASPNETCORE_URLS=http://+:80

# إعدادات قاعدة البيانات
CONNECTION_STRING=Data Source=cryptobot.db

# إعدادات الأمان
JWT_SECRET_KEY=your-super-secret-jwt-key-here-make-it-long-and-complex
ENCRYPTION_KEY=your-32-character-encryption-key

# إعدادات APIs الخارجية
DEEPSEEK_API_KEY=your-deepseek-api-key-here
NEWSAPI_KEY=your-newsapi-key-here

# إعدادات التداول
DEFAULT_TRADING_MODE=PAPER
MAX_TOTAL_INVESTMENT=10000
MAX_INVESTMENT_PER_TRADE=1000
GLOBAL_STOP_LOSS=10.0
GLOBAL_TAKE_PROFIT=20.0

# إعدادات جمع البيانات
DATA_COLLECTION_INTERVAL=30
PRICE_UPDATE_INTERVAL=30
NEWS_UPDATE_INTERVAL=300

# إعدادات التسجيل
LOG_LEVEL=Information
LOG_FILE_PATH=logs/cryptobot.log

# إعدادات الإشعارات
NOTIFICATIONS_ENABLED=true
EMAIL_NOTIFICATIONS=false
PHONE_NOTIFICATIONS=false

# إعدادات الأداء
CACHE_DURATION=300
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30

# إعدادات Docker (إذا كنت تستخدم Docker)
POSTGRES_DB=cryptobot
POSTGRES_USER=admin
POSTGRES_PASSWORD=CryptoBot123!
REDIS_URL=redis://localhost:6379
