# إعدادات Docker Compose للتطوير
version: '3.8'

services:
  cryptotradingbot:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
    volumes:
      - .:/app/source:ro
      - ./data:/app/data
      - ./logs:/app/logs
    ports:
      - "5168:80"  # نفس المنفذ المستخدم في التطوير
    build:
      target: development
    
  nginx:
    ports:
      - "8080:80"  # تغيير المنفذ لتجنب التعارض
      
  grafana:
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
      
  prometheus:
    ports:
      - "9091:9090"  # تغيير المنفذ لتجنب التعارض
