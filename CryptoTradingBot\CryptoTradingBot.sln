﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CryptoTradingBot", "CryptoTradingBot.csproj", "{FD51B9D3-3E94-4940-98D4-F8B7E668044D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Docker", "Docker", "{D1E2F3A4-B5C6-7890-ABCD-EF1234567890}"
	ProjectSection(SolutionItems) = preProject
		docker-compose.yml = docker-compose.yml
		docker-compose.override.yml = docker-compose.override.yml
		docker-compose.prod.yml = docker-compose.prod.yml
		docker-compose.test.yml = docker-compose.test.yml
		Dockerfile = Dockerfile
		.dockerignore = .dockerignore
		.env.example = .env.example
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Configuration", "Configuration", "{E2F3A4B5-C6D7-8901-BCDE-F12345678901}"
	ProjectSection(SolutionItems) = preProject
		nginx\nginx.conf = nginx\nginx.conf
		redis\redis.conf = redis\redis.conf
		prometheus\prometheus.yml = prometheus\prometheus.yml
		grafana\datasources\prometheus.yml = grafana\datasources\prometheus.yml
		grafana\dashboards\dashboard.yml = grafana\dashboards\dashboard.yml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CI-CD", "CI-CD", "{A4B5C6D7-E8F9-0123-DEFG-************}"
	ProjectSection(SolutionItems) = preProject
		.github\workflows\docker-ci.yml = .github\workflows\docker-ci.yml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Documentation", "Documentation", "{F3A4B5C6-D7E8-9012-CDEF-123456789012}"
	ProjectSection(SolutionItems) = preProject
		README.md = README.md
		DOCKER_README.md = DOCKER_README.md
		DOCKER_COMPOSE_REPORT.md = DOCKER_COMPOSE_REPORT.md
		INSTALLATION_GUIDE.md = INSTALLATION_GUIDE.md
		PROJECT_VERIFICATION_REPORT.md = PROJECT_VERIFICATION_REPORT.md
		SOLUTION_ACTIVATION_REPORT.md = SOLUTION_ACTIVATION_REPORT.md
		Makefile = Makefile
	EndProjectSection
EndProject

Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FD51B9D3-3E94-4940-98D4-F8B7E668044D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FD51B9D3-3E94-4940-98D4-F8B7E668044D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FD51B9D3-3E94-4940-98D4-F8B7E668044D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{FD51B9D3-3E94-4940-98D4-F8B7E668044D}.Debug|x64.Build.0 = Debug|Any CPU
		{FD51B9D3-3E94-4940-98D4-F8B7E668044D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{FD51B9D3-3E94-4940-98D4-F8B7E668044D}.Debug|x86.Build.0 = Debug|Any CPU
		{FD51B9D3-3E94-4940-98D4-F8B7E668044D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FD51B9D3-3E94-4940-98D4-F8B7E668044D}.Release|Any CPU.Build.0 = Release|Any CPU
		{FD51B9D3-3E94-4940-98D4-F8B7E668044D}.Release|x64.ActiveCfg = Release|Any CPU
		{FD51B9D3-3E94-4940-98D4-F8B7E668044D}.Release|x64.Build.0 = Release|Any CPU
		{FD51B9D3-3E94-4940-98D4-F8B7E668044D}.Release|x86.ActiveCfg = Release|Any CPU
		{FD51B9D3-3E94-4940-98D4-F8B7E668044D}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
