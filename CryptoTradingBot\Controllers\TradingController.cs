using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using CryptoTradingBot.Data;
using CryptoTradingBot.Services;
using CryptoTradingBot.Models;
using System.Security.Claims;

namespace CryptoTradingBot.Controllers
{
    /// <summary>
    /// تحكم في عمليات التداول
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TradingController : ControllerBase
    {
        private readonly CryptoTradingDbContext _context;
        private readonly ITradingService _tradingService;
        private readonly IDeepSeekService _deepSeekService;
        private readonly ITechnicalAnalysisService _technicalAnalysisService;
        private readonly ILogger<TradingController> _logger;

        public TradingController(
            CryptoTradingDbContext context,
            ITradingService tradingService,
            IDeepSeekService deepSeekService,
            ITechnicalAnalysisService technicalAnalysisService,
            ILogger<TradingController> logger)
        {
            _context = context;
            _tradingService = tradingService;
            _deepSeekService = deepSeekService;
            _technicalAnalysisService = technicalAnalysisService;
            _logger = logger;
        }

        /// <summary>
        /// تنفيذ صفقة تداول
        /// </summary>
        [HttpPost("execute")]
        public async Task<IActionResult> ExecuteTrade([FromBody] ExecuteTradeRequest request)
        {
            try
            {
                var currency = await _context.CryptoCurrencies
                    .FirstOrDefaultAsync(c => c.Symbol == request.Symbol && c.IsActive);

                if (currency == null)
                {
                    return NotFound(new { message = "العملة غير موجودة" });
                }

                var operation = new TradingOperation
                {
                    CryptoCurrencyId = currency.Id,
                    Type = request.Type,
                    Amount = request.Amount,
                    Mode = request.Mode ?? "PAPER",
                    Strategy = request.Strategy ?? "MANUAL",
                    Notes = request.Notes
                };

                var result = await _tradingService.ExecuteTradeAsync(operation);

                if (result.Success)
                {
                    await LogActivity("EXECUTE_TRADE", "TradingOperation", result.OperationId?.ToString(), 
                        $"Executed {request.Type} trade for {request.Symbol}");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing trade");
                return StatusCode(500, new { message = "خطأ في تنفيذ الصفقة" });
            }
        }

        /// <summary>
        /// الحصول على الصفقات النشطة
        /// </summary>
        [HttpGet("active")]
        public async Task<IActionResult> GetActiveTrades()
        {
            try
            {
                var activeTrades = await _tradingService.GetActiveTradesAsync();
                
                // إضافة معلومات إضافية لكل صفقة
                var enrichedTrades = new List<object>();
                
                foreach (var trade in activeTrades)
                {
                    var currentPrice = trade.CryptoCurrency?.CurrentPrice ?? 0;
                    var profitLoss = _tradingService.CalculateProfitLoss(trade, currentPrice);
                    var profitLossPercentage = trade.TotalValue > 0 ? (profitLoss / trade.TotalValue) * 100 : 0;

                    enrichedTrades.Add(new
                    {
                        trade.Id,
                        trade.CryptoCurrency?.Symbol,
                        trade.CryptoCurrency?.Name,
                        trade.Type,
                        trade.Amount,
                        trade.Price,
                        trade.TotalValue,
                        CurrentPrice = currentPrice,
                        ProfitLoss = profitLoss,
                        ProfitLossPercentage = profitLossPercentage,
                        trade.Status,
                        trade.Mode,
                        trade.Strategy,
                        trade.CreatedAt,
                        trade.ExecutedAt
                    });
                }

                return Ok(enrichedTrades);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active trades");
                return StatusCode(500, new { message = "خطأ في جلب الصفقات النشطة" });
            }
        }

        /// <summary>
        /// الحصول على سجل التداول
        /// </summary>
        [HttpGet("history")]
        public async Task<IActionResult> GetTradingHistory([FromQuery] int page = 1, [FromQuery] int pageSize = 20)
        {
            try
            {
                var skip = (page - 1) * pageSize;
                
                var trades = await _context.TradingOperations
                    .Include(t => t.CryptoCurrency)
                    .OrderByDescending(t => t.CreatedAt)
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                var totalCount = await _context.TradingOperations.CountAsync();

                return Ok(new
                {
                    trades,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting trading history");
                return StatusCode(500, new { message = "خطأ في جلب سجل التداول" });
            }
        }

        /// <summary>
        /// إلغاء صفقة
        /// </summary>
        [HttpPost("cancel/{operationId}")]
        public async Task<IActionResult> CancelTrade(long operationId)
        {
            try
            {
                var success = await _tradingService.CancelTradeAsync(operationId);
                
                if (success)
                {
                    await LogActivity("CANCEL_TRADE", "TradingOperation", operationId.ToString(), 
                        $"Cancelled trade {operationId}");
                    return Ok(new { message = "تم إلغاء الصفقة بنجاح" });
                }

                return BadRequest(new { message = "لا يمكن إلغاء هذه الصفقة" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error cancelling trade {operationId}");
                return StatusCode(500, new { message = "خطأ في إلغاء الصفقة" });
            }
        }

        /// <summary>
        /// الحصول على إحصائيات التداول
        /// </summary>
        [HttpGet("statistics")]
        public async Task<IActionResult> GetTradingStatistics()
        {
            try
            {
                var userId = GetCurrentUserId();
                var statistics = await _tradingService.GetTradingStatisticsAsync(userId);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting trading statistics");
                return StatusCode(500, new { message = "خطأ في جلب إحصائيات التداول" });
            }
        }

        /// <summary>
        /// الحصول على توصية تداول ذكية
        /// </summary>
        [HttpPost("smart-recommendation/{symbol}")]
        public async Task<IActionResult> GetSmartRecommendation(string symbol)
        {
            try
            {
                var currency = await _context.CryptoCurrencies
                    .FirstOrDefaultAsync(c => c.Symbol == symbol && c.IsActive);

                if (currency == null)
                {
                    return NotFound(new { message = "العملة غير موجودة" });
                }

                // التحليل الفني
                var technicalAnalysis = await _technicalAnalysisService.GetComprehensiveAnalysisAsync(symbol);

                // تحليل المشاعر
                var sentimentResult = await _context.NewsData
                    .Where(n => n.Keywords.Contains(symbol.ToLower()) && 
                               n.PublishedAt >= DateTime.UtcNow.AddHours(-24))
                    .ToListAsync();

                // توصية الذكاء الاصطناعي
                var aiRecommendation = await _deepSeekService.AnalyzeAndRecommendAsync(
                    symbol, 
                    technicalAnalysis, 
                    sentimentResult);

                // دمج التوصيات
                var combinedRecommendation = new
                {
                    Symbol = symbol,
                    CurrentPrice = currency.CurrentPrice,
                    TechnicalAnalysis = new
                    {
                        technicalAnalysis.OverallSignal,
                        technicalAnalysis.SignalStrength,
                        technicalAnalysis.RSI,
                        technicalAnalysis.TrendAnalysis.OverallTrend,
                        technicalAnalysis.Volatility
                    },
                    AIRecommendation = aiRecommendation,
                    SentimentAnalysis = new
                    {
                        NewsCount = sentimentResult.Count,
                        AverageSentiment = sentimentResult.Any() ? sentimentResult.Average(n => n.SentimentScore) : 0,
                        OverallSentiment = sentimentResult.Any() ? 
                            (sentimentResult.Average(n => n.SentimentScore) > 0.1m ? "POSITIVE" : 
                             sentimentResult.Average(n => n.SentimentScore) < -0.1m ? "NEGATIVE" : "NEUTRAL") : "NEUTRAL"
                    },
                    FinalRecommendation = DetermineFinalRecommendation(technicalAnalysis, aiRecommendation, sentimentResult),
                    GeneratedAt = DateTime.UtcNow
                };

                return Ok(combinedRecommendation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting smart recommendation for {symbol}");
                return StatusCode(500, new { message = "خطأ في الحصول على التوصية الذكية" });
            }
        }

        /// <summary>
        /// تحديث الصفقات النشطة
        /// </summary>
        [HttpPost("update-active")]
        public async Task<IActionResult> UpdateActiveTrades()
        {
            try
            {
                await _tradingService.UpdateActiveTradesAsync();
                return Ok(new { message = "تم تحديث الصفقات النشطة بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating active trades");
                return StatusCode(500, new { message = "خطأ في تحديث الصفقات النشطة" });
            }
        }

        /// <summary>
        /// محاكاة استراتيجية تداول
        /// </summary>
        [HttpPost("backtest-strategy")]
        public async Task<IActionResult> BacktestStrategy([FromBody] BacktestRequest request)
        {
            try
            {
                var strategy = await _context.TradingStrategies
                    .FirstOrDefaultAsync(s => s.Id == request.StrategyId);

                if (strategy == null)
                {
                    return NotFound(new { message = "الاستراتيجية غير موجودة" });
                }

                // تنفيذ محاكاة الاستراتيجية
                var backtestResult = await PerformBacktest(strategy, request.StartDate, request.EndDate, request.InitialBalance);

                // حفظ نتائج المحاكاة
                var backtest = new StrategyBacktest
                {
                    TradingStrategyId = request.StrategyId,
                    StartDate = request.StartDate,
                    EndDate = request.EndDate,
                    InitialBalance = request.InitialBalance,
                    FinalBalance = backtestResult.FinalBalance,
                    TotalReturn = backtestResult.TotalReturn,
                    ReturnPercentage = backtestResult.ReturnPercentage,
                    TotalTrades = backtestResult.TotalTrades,
                    WinningTrades = backtestResult.WinningTrades,
                    WinRate = backtestResult.WinRate,
                    MaxDrawdown = backtestResult.MaxDrawdown,
                    SharpeRatio = backtestResult.SharpeRatio,
                    DetailedResults = System.Text.Json.JsonSerializer.Serialize(backtestResult.DetailedResults)
                };

                _context.StrategyBacktests.Add(backtest);
                await _context.SaveChangesAsync();

                return Ok(backtestResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing backtest");
                return StatusCode(500, new { message = "خطأ في تنفيذ المحاكاة" });
            }
        }

        private string GetCurrentUserId()
        {
            return User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "default";
        }

        private async Task LogActivity(string action, string entity, string entityId, string description)
        {
            var log = new ActivityLog
            {
                UserId = GetCurrentUserId(),
                Action = action,
                Entity = entity,
                EntityId = entityId,
                Description = description,
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString()
            };

            _context.ActivityLogs.Add(log);
            await _context.SaveChangesAsync();
        }

        private object DetermineFinalRecommendation(ComprehensiveTechnicalAnalysis technical, TradingRecommendation ai, List<NewsData> sentiment)
        {
            var technicalScore = technical.OverallSignal switch
            {
                "BUY" => 1,
                "SELL" => -1,
                _ => 0
            };

            var aiScore = ai.Action switch
            {
                "BUY" => 1,
                "SELL" => -1,
                _ => 0
            };

            var sentimentScore = sentiment.Any() ? 
                (sentiment.Average(n => n.SentimentScore) > 0.1m ? 1 : 
                 sentiment.Average(n => n.SentimentScore) < -0.1m ? -1 : 0) : 0;

            var totalScore = (technicalScore * 0.4) + (aiScore * 0.4) + (sentimentScore * 0.2);
            var confidence = (technical.SignalStrength + ai.Confidence) / 2;

            var finalAction = totalScore switch
            {
                > 0.3 => "BUY",
                < -0.3 => "SELL",
                _ => "HOLD"
            };

            return new
            {
                Action = finalAction,
                Confidence = confidence,
                Score = totalScore,
                Reasoning = $"Technical: {technical.OverallSignal}, AI: {ai.Action}, Sentiment: {(sentimentScore > 0 ? "Positive" : sentimentScore < 0 ? "Negative" : "Neutral")}"
            };
        }

        private async Task<BacktestResult> PerformBacktest(TradingStrategy strategy, DateTime startDate, DateTime endDate, decimal initialBalance)
        {
            // محاكاة بسيطة - في التطبيق الحقيقي ستكون أكثر تعقيداً
            await Task.Delay(1000); // محاكاة وقت المعالجة

            var random = new Random();
            var totalTrades = random.Next(50, 200);
            var winningTrades = (int)(totalTrades * (0.4 + random.NextDouble() * 0.4)); // 40-80% win rate
            var totalReturn = (decimal)(random.NextDouble() * 0.5 - 0.1); // -10% to +40%
            var finalBalance = initialBalance * (1 + totalReturn);

            return new BacktestResult
            {
                InitialBalance = initialBalance,
                FinalBalance = finalBalance,
                TotalReturn = finalBalance - initialBalance,
                ReturnPercentage = totalReturn * 100,
                TotalTrades = totalTrades,
                WinningTrades = winningTrades,
                WinRate = (decimal)winningTrades / totalTrades * 100,
                MaxDrawdown = (decimal)(random.NextDouble() * 0.2), // 0-20%
                SharpeRatio = (decimal)(random.NextDouble() * 2), // 0-2
                DetailedResults = new { message = "Detailed backtest results would be here" }
            };
        }
    }

    // نماذج الطلبات
    public class ExecuteTradeRequest
    {
        public string Symbol { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // BUY, SELL
        public decimal Amount { get; set; }
        public string? Mode { get; set; } = "PAPER";
        public string? Strategy { get; set; }
        public string? Notes { get; set; }
    }

    public class BacktestRequest
    {
        public int StrategyId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal InitialBalance { get; set; } = 10000;
    }

    public class BacktestResult
    {
        public decimal InitialBalance { get; set; }
        public decimal FinalBalance { get; set; }
        public decimal TotalReturn { get; set; }
        public decimal ReturnPercentage { get; set; }
        public int TotalTrades { get; set; }
        public int WinningTrades { get; set; }
        public decimal WinRate { get; set; }
        public decimal MaxDrawdown { get; set; }
        public decimal SharpeRatio { get; set; }
        public object DetailedResults { get; set; } = new();
    }
}
