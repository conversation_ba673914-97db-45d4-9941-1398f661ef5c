using CryptoTradingBot.Models;

namespace CryptoTradingBot.Services
{
    /// <summary>
    /// واجهة خدمة جمع البيانات
    /// </summary>
    public interface IDataCollectionService
    {
        /// <summary>
        /// جمع البيانات الحالية للأسعار
        /// </summary>
        /// <param name="symbols">رموز العملات</param>
        /// <returns>قائمة بالأسعار الحالية</returns>
        Task<List<CryptoCurrency>> GetCurrentPricesAsync(List<string> symbols);

        /// <summary>
        /// جمع البيانات التاريخية للأسعار
        /// </summary>
        /// <param name="symbol">رمز العملة</param>
        /// <param name="days">عدد الأيام</param>
        /// <param name="interval">الفترة الزمنية</param>
        /// <returns>قائمة بالبيانات التاريخية</returns>
        Task<List<PriceHistory>> GetHistoricalPricesAsync(string symbol, int days, string interval = "1h");

        /// <summary>
        /// جمع أخبار العملات المشفرة
        /// </summary>
        /// <param name="keywords">الكلمات المفتاحية</param>
        /// <param name="hours">عدد الساعات الماضية</param>
        /// <returns>قائمة بالأخبار</returns>
        Task<List<NewsData>> GetCryptoNewsAsync(List<string> keywords, int hours = 24);

        /// <summary>
        /// تحديث البيانات في قاعدة البيانات
        /// </summary>
        /// <param name="currencies">العملات المحدثة</param>
        Task UpdateCurrenciesAsync(List<CryptoCurrency> currencies);

        /// <summary>
        /// حفظ البيانات التاريخية
        /// </summary>
        /// <param name="priceHistories">البيانات التاريخية</param>
        Task SavePriceHistoriesAsync(List<PriceHistory> priceHistories);

        /// <summary>
        /// حفظ الأخبار
        /// </summary>
        /// <param name="newsData">بيانات الأخبار</param>
        Task SaveNewsDataAsync(List<NewsData> newsData);

        /// <summary>
        /// التحقق من حالة APIs
        /// </summary>
        /// <returns>حالة الـ APIs</returns>
        Task<Dictionary<string, bool>> CheckApiStatusAsync();
    }
}
