<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بوت تداول العملات المشفرة - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-robot me-2"></i>
                بوت تداول العملات المشفرة
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#dashboard" onclick="showSection('dashboard')">
                            <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#settings" onclick="showSection('settings')">
                            <i class="fas fa-cog me-1"></i>الإعدادات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#api-config" onclick="showSection('api-config')">
                            <i class="fas fa-key me-1"></i>إعدادات APIs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#trading" onclick="showSection('trading')">
                            <i class="fas fa-chart-line me-1"></i>التداول
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#analysis" onclick="showSection('analysis')">
                            <i class="fas fa-chart-area me-1"></i>التحليل الفني
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-1"></i>تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- نموذج تسجيل الدخول -->
    <div id="login-modal" class="modal fade" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تسجيل الدخول</h5>
                </div>
                <div class="modal-body">
                    <form id="login-form">
                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="username" value="admin" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="password" value="admin123" required>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">دخول</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid mt-4" id="main-content" style="display: none;">
        
        <!-- لوحة التحكم -->
        <div id="dashboard" class="content-section">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="total-balance">$0.00</h4>
                                    <p>الرصيد الإجمالي</p>
                                </div>
                                <i class="fas fa-wallet fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="total-profit">$0.00</h4>
                                    <p>إجمالي الربح</p>
                                </div>
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="active-trades">0</h4>
                                    <p>الصفقات النشطة</p>
                                </div>
                                <i class="fas fa-exchange-alt fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="win-rate">0%</h4>
                                    <p>معدل النجاح</p>
                                </div>
                                <i class="fas fa-percentage fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5>الأسعار الحالية</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped" id="prices-table">
                                    <thead>
                                        <tr>
                                            <th>العملة</th>
                                            <th>السعر</th>
                                            <th>التغيير 24س</th>
                                            <th>الحجم</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>آخر الأنشطة</h5>
                        </div>
                        <div class="card-body">
                            <div id="activity-log">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإعدادات -->
        <div id="settings" class="content-section" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5>إعدادات التداول</h5>
                </div>
                <div class="card-body">
                    <form id="settings-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="trading-mode" class="form-label">وضع التداول</label>
                                    <select class="form-select" id="trading-mode">
                                        <option value="PAPER">تداول وهمي</option>
                                        <option value="LIVE">تداول حقيقي</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="max-investment" class="form-label">الحد الأقصى للاستثمار ($)</label>
                                    <input type="number" class="form-control" id="max-investment" step="0.01">
                                </div>
                                <div class="mb-3">
                                    <label for="max-per-trade" class="form-label">الحد الأقصى لكل صفقة ($)</label>
                                    <input type="number" class="form-control" id="max-per-trade" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="risk-level" class="form-label">مستوى المخاطرة (1-5)</label>
                                    <input type="range" class="form-range" id="risk-level" min="1" max="5" step="1">
                                    <div class="d-flex justify-content-between">
                                        <small>منخفض</small>
                                        <small>عالي</small>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="stop-loss" class="form-label">وقف الخسارة (%)</label>
                                    <input type="number" class="form-control" id="stop-loss" step="0.1">
                                </div>
                                <div class="mb-3">
                                    <label for="take-profit" class="form-label">جني الأرباح (%)</label>
                                    <input type="number" class="form-control" id="take-profit" step="0.1">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="auto-trading">
                                <label class="form-check-label" for="auto-trading">
                                    تفعيل التداول التلقائي
                                </label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- إعدادات APIs -->
        <div id="api-config" class="content-section" style="display: none;">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>إعدادات APIs</h5>
                    <button class="btn btn-primary btn-sm" onclick="showAddApiModal()">
                        <i class="fas fa-plus me-1"></i>إضافة API
                    </button>
                </div>
                <div class="card-body">
                    <div id="api-configs-list">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- التداول -->
        <div id="trading" class="content-section" style="display: none;">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5>سجل العمليات</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped" id="trades-table">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>العملة</th>
                                            <th>النوع</th>
                                            <th>الكمية</th>
                                            <th>السعر</th>
                                            <th>الربح/الخسارة</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>تنفيذ صفقة يدوية</h5>
                        </div>
                        <div class="card-body">
                            <form id="manual-trade-form">
                                <div class="mb-3">
                                    <label for="trade-currency" class="form-label">العملة</label>
                                    <select class="form-select" id="trade-currency">
                                        <option value="BTC">Bitcoin (BTC)</option>
                                        <option value="ETH">Ethereum (ETH)</option>
                                        <option value="ADA">Cardano (ADA)</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="trade-type" class="form-label">نوع العملية</label>
                                    <select class="form-select" id="trade-type">
                                        <option value="BUY">شراء</option>
                                        <option value="SELL">بيع</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="trade-amount" class="form-label">المبلغ ($)</label>
                                    <input type="number" class="form-control" id="trade-amount" step="0.01">
                                </div>
                                <button type="submit" class="btn btn-success w-100">تنفيذ الصفقة</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- التحليل الفني -->
        <div id="analysis" class="content-section" style="display: none;">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5>التحليل الفني</h5>
                            <select class="form-select w-auto" id="analysis-currency">
                                <option value="BTC">Bitcoin (BTC)</option>
                                <option value="ETH">Ethereum (ETH)</option>
                                <option value="ADA">Cardano (ADA)</option>
                            </select>
                        </div>
                        <div class="card-body">
                            <div id="technical-analysis-content">
                                <div class="text-center">
                                    <button class="btn btn-primary" onclick="app.loadTechnicalAnalysis()">
                                        <i class="fas fa-chart-line me-2"></i>تحليل العملة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>توصية ذكية</h5>
                        </div>
                        <div class="card-body">
                            <div id="smart-recommendation-content">
                                <div class="text-center">
                                    <button class="btn btn-success" onclick="app.getSmartRecommendation()">
                                        <i class="fas fa-robot me-2"></i>الحصول على توصية
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>تحليل السوق العام</h5>
                        </div>
                        <div class="card-body">
                            <div id="market-analysis-content">
                                <div class="text-center">
                                    <button class="btn btn-info" onclick="app.loadMarketAnalysis()">
                                        <i class="fas fa-chart-area me-2"></i>تحليل السوق
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة API -->
    <div id="add-api-modal" class="modal fade" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة تكوين API</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="add-api-form">
                        <div class="mb-3">
                            <label for="api-name" class="form-label">اسم API</label>
                            <select class="form-select" id="api-name">
                                <option value="DeepSeek">DeepSeek AI</option>
                                <option value="CoinGecko">CoinGecko</option>
                                <option value="Binance">Binance</option>
                                <option value="AlphaVantage">Alpha Vantage</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="api-key" class="form-label">API Key</label>
                            <input type="text" class="form-control" id="api-key" required>
                        </div>
                        <div class="mb-3">
                            <label for="api-secret" class="form-label">API Secret (اختياري)</label>
                            <input type="password" class="form-control" id="api-secret">
                        </div>
                        <div class="mb-3">
                            <label for="base-url" class="form-label">Base URL (اختياري)</label>
                            <input type="url" class="form-control" id="base-url">
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is-testnet">
                                <label class="form-check-label" for="is-testnet">
                                    شبكة اختبار
                                </label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
