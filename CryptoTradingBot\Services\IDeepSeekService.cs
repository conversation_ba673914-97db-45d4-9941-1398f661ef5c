namespace CryptoTradingBot.Services
{
    /// <summary>
    /// واجهة خدمة DeepSeek AI
    /// </summary>
    public interface IDeepSeekService
    {
        /// <summary>
        /// تحليل البيانات وإعطاء توصية تداول
        /// </summary>
        /// <param name="symbol">رمز العملة</param>
        /// <param name="priceData">بيانات الأسعار</param>
        /// <param name="newsData">بيانات الأخبار</param>
        /// <returns>توصية التداول</returns>
        Task<TradingRecommendation> AnalyzeAndRecommendAsync(string symbol, object priceData, object newsData);

        /// <summary>
        /// تحليل المشاعر من النص
        /// </summary>
        /// <param name="text">النص المراد تحليله</param>
        /// <returns>نتيجة تحليل المشاعر</returns>
        Task<SentimentAnalysisResult> AnalyzeSentimentAsync(string text);

        /// <summary>
        /// التنبؤ بالأسعار
        /// </summary>
        /// <param name="symbol">رمز العملة</param>
        /// <param name="historicalData">البيانات التاريخية</param>
        /// <param name="timeframe">الإطار الزمني للتنبؤ</param>
        /// <returns>توقع السعر</returns>
        Task<PricePrediction> PredictPriceAsync(string symbol, object historicalData, string timeframe);

        /// <summary>
        /// تحسين استراتيجية التداول
        /// </summary>
        /// <param name="strategyId">معرف الاستراتيجية</param>
        /// <param name="performanceData">بيانات الأداء</param>
        /// <returns>اقتراحات التحسين</returns>
        Task<StrategyOptimization> OptimizeStrategyAsync(int strategyId, object performanceData);
    }

    /// <summary>
    /// نموذج توصية التداول
    /// </summary>
    public class TradingRecommendation
    {
        public string Symbol { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty; // BUY, SELL, HOLD
        public decimal Confidence { get; set; }
        public decimal SuggestedAmount { get; set; }
        public decimal? StopLoss { get; set; }
        public decimal? TakeProfit { get; set; }
        public string Reasoning { get; set; } = string.Empty;
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// نموذج نتيجة تحليل المشاعر
    /// </summary>
    public class SentimentAnalysisResult
    {
        public decimal Score { get; set; } // -1.0 to 1.0
        public string Label { get; set; } = string.Empty; // POSITIVE, NEGATIVE, NEUTRAL
        public decimal Confidence { get; set; }
        public Dictionary<string, decimal> Emotions { get; set; } = new();
    }

    /// <summary>
    /// نموذج توقع السعر
    /// </summary>
    public class PricePrediction
    {
        public string Symbol { get; set; } = string.Empty;
        public decimal CurrentPrice { get; set; }
        public decimal PredictedPrice { get; set; }
        public decimal PriceChange { get; set; }
        public decimal ChangePercentage { get; set; }
        public decimal Confidence { get; set; }
        public string Timeframe { get; set; } = string.Empty;
        public DateTime PredictionDate { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// نموذج تحسين الاستراتيجية
    /// </summary>
    public class StrategyOptimization
    {
        public int StrategyId { get; set; }
        public List<string> Suggestions { get; set; } = new();
        public Dictionary<string, object> OptimizedParameters { get; set; } = new();
        public decimal ExpectedImprovement { get; set; }
        public string Explanation { get; set; } = string.Empty;
    }
}
