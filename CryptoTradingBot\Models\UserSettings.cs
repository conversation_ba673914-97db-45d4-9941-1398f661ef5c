using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CryptoTradingBot.Models
{
    /// <summary>
    /// نموذج إعدادات المستخدم والنظام
    /// </summary>
    public class UserSettings
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string UserId { get; set; } = "default";

        [StringLength(20)]
        public string TradingMode { get; set; } = "PAPER"; // PAPER, LIVE

        [Column(TypeName = "decimal(18,8)")]
        public decimal MaxTotalInvestment { get; set; } = 1000m;

        [Column(TypeName = "decimal(18,8)")]
        public decimal MaxInvestmentPerTrade { get; set; } = 100m;

        [Column(TypeName = "decimal(5,2)")]
        public decimal RiskLevel { get; set; } = 3m; // 1-5 scale

        [Column(TypeName = "decimal(5,2)")]
        public decimal GlobalStopLoss { get; set; } = 10m; // Global stop loss percentage

        [Column(TypeName = "decimal(5,2)")]
        public decimal GlobalTakeProfit { get; set; } = 20m; // Global take profit percentage

        public bool AutoTradingEnabled { get; set; } = false;

        public bool NotificationsEnabled { get; set; } = true;

        [StringLength(100)]
        public string? EmailNotifications { get; set; }

        [StringLength(20)]
        public string? PhoneNotifications { get; set; }

        [StringLength(200)]
        public string? PreferredCurrencies { get; set; } = "BTC,ETH,ADA"; // Comma separated

        public int MaxConcurrentTrades { get; set; } = 5;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // إعدادات APIs
        public virtual ICollection<ApiConfiguration> ApiConfigurations { get; set; } = new List<ApiConfiguration>();
    }

    /// <summary>
    /// نموذج تكوين APIs
    /// </summary>
    public class ApiConfiguration
    {
        [Key]
        public int Id { get; set; }

        public int UserSettingsId { get; set; }

        [Required]
        [StringLength(50)]
        public string ApiName { get; set; } = string.Empty; // DeepSeek, Binance, CoinGecko, etc.

        [Required]
        [StringLength(500)]
        public string ApiKey { get; set; } = string.Empty; // Encrypted

        [StringLength(500)]
        public string? ApiSecret { get; set; } // Encrypted

        [StringLength(200)]
        public string? BaseUrl { get; set; }

        public bool IsActive { get; set; } = true;

        public bool IsTestnet { get; set; } = false;

        [Column(TypeName = "text")]
        public string? AdditionalSettings { get; set; } // JSON for extra settings

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // العلاقات
        [ForeignKey("UserSettingsId")]
        public virtual UserSettings UserSettings { get; set; } = null!;
    }

    /// <summary>
    /// نموذج سجل النشاطات
    /// </summary>
    public class ActivityLog
    {
        [Key]
        public long Id { get; set; }

        [StringLength(100)]
        public string? UserId { get; set; }

        [Required]
        [StringLength(50)]
        public string Action { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Entity { get; set; }

        [StringLength(50)]
        public string? EntityId { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(20)]
        public string Level { get; set; } = "INFO"; // INFO, WARNING, ERROR

        [Column(TypeName = "text")]
        public string? Details { get; set; } // JSON details

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [StringLength(50)]
        public string? IpAddress { get; set; }

        [StringLength(200)]
        public string? UserAgent { get; set; }
    }
}
