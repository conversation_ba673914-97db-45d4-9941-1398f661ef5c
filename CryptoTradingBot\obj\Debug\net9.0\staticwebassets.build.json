{"Version": 1, "Hash": "oy8jaO5Io37NOaXGkUafi49L+3o7gyMMUWW5mdnQa94=", "Source": "CryptoTradingBot", "BasePath": "_content/CryptoTradingBot", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "CryptoTradingBot\\wwwroot", "Source": "CryptoTradingBot", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\", "BasePath": "_content/CryptoTradingBot", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\dff47ilxk8-u544at3w8a.gz", "SourceId": "CryptoTradingBot", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CryptoTradingBot", "RelativePath": "index#[.{fingerprint=u544at3w8a}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "94i4v5vnai", "Integrity": "uZdP155yp3wra21GOF4qDMYiwxN32dAZvXmSFeCE25E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\index.html", "FileLength": 3209, "LastWriteTime": "2025-07-18T17:42:28+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\v4sb0oc98t-87naruipz6.gz", "SourceId": "CryptoTradingBot", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CryptoTradingBot", "RelativePath": "css/style#[.{fingerprint=87naruipz6}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\css\\style.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ptqhultulg", "Integrity": "DUTSSMRlkapzO+15WZpafVKmgmp/dkqddJHF9iLHyGU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\css\\style.css", "FileLength": 1731, "LastWriteTime": "2025-07-18T17:42:28+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\wqulb7x2k4-flty0zdg65.gz", "SourceId": "CryptoTradingBot", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CryptoTradingBot", "RelativePath": "js/app#[.{fingerprint=flty0zdg65}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\js\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "egtfgsqo3g", "Integrity": "3uIPZLrF8A476VVljjE28lMHu0M76nEEBT6HnFeYKWo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\js\\app.js", "FileLength": 5770, "LastWriteTime": "2025-07-18T17:42:28+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\css\\style.css", "SourceId": "CryptoTradingBot", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\", "BasePath": "_content/CryptoTradingBot", "RelativePath": "css/style#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "87naruipz6", "Integrity": "DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\style.css", "FileLength": 5938, "LastWriteTime": "2025-07-18T16:51:57+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\index.html", "SourceId": "CryptoTradingBot", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\", "BasePath": "_content/CryptoTradingBot", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u544at3w8a", "Integrity": "Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 22216, "LastWriteTime": "2025-07-18T17:26:23+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\js\\app.js", "SourceId": "CryptoTradingBot", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\", "BasePath": "_content/CryptoTradingBot", "RelativePath": "js/app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "flty0zdg65", "Integrity": "Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\app.js", "FileLength": 30935, "LastWriteTime": "2025-07-18T17:27:13+00:00"}], "Endpoints": [{"Route": "css/style.87naruipz6.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\v4sb0oc98t-87naruipz6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000577367206"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1731"}, {"Name": "ETag", "Value": "\"DUTSSMRlkapzO+15WZpafVKmgmp/dkqddJHF9iLHyGU=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:42:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87naruipz6"}, {"Name": "label", "Value": "css/style.css"}, {"Name": "integrity", "Value": "sha256-DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs="}]}, {"Route": "css/style.87naruipz6.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\css\\style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5938"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 16:51:57 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87naruipz6"}, {"Name": "label", "Value": "css/style.css"}, {"Name": "integrity", "Value": "sha256-DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs="}]}, {"Route": "css/style.87naruipz6.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\v4sb0oc98t-87naruipz6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1731"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DUTSSMRlkapzO+15WZpafVKmgmp/dkqddJHF9iLHyGU=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:42:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87naruipz6"}, {"Name": "label", "Value": "css/style.css.gz"}, {"Name": "integrity", "Value": "sha256-DUTSSMRlkapzO+15WZpafVKmgmp/dkqddJHF9iLHyGU="}]}, {"Route": "css/style.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\v4sb0oc98t-87naruipz6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000577367206"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1731"}, {"Name": "ETag", "Value": "\"DUTSSMRlkapzO+15WZpafVKmgmp/dkqddJHF9iLHyGU=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:42:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs="}]}, {"Route": "css/style.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\css\\style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5938"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 16:51:57 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs="}]}, {"Route": "css/style.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\v4sb0oc98t-87naruipz6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1731"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DUTSSMRlkapzO+15WZpafVKmgmp/dkqddJHF9iLHyGU=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:42:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DUTSSMRlkapzO+15WZpafVKmgmp/dkqddJHF9iLHyGU="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\dff47ilxk8-u544at3w8a.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000311526480"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3209"}, {"Name": "ETag", "Value": "\"uZdP155yp3wra21GOF4qDMYiwxN32dAZvXmSFeCE25E=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:42:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22216"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:26:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\dff47ilxk8-u544at3w8a.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3209"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"uZdP155yp3wra21GOF4qDMYiwxN32dAZvXmSFeCE25E=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:42:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uZdP155yp3wra21GOF4qDMYiwxN32dAZvXmSFeCE25E="}]}, {"Route": "index.u544at3w8a.html", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\dff47ilxk8-u544at3w8a.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000311526480"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3209"}, {"Name": "ETag", "Value": "\"uZdP155yp3wra21GOF4qDMYiwxN32dAZvXmSFeCE25E=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:42:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u544at3w8a"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA="}]}, {"Route": "index.u544at3w8a.html", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22216"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:26:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u544at3w8a"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA="}]}, {"Route": "index.u544at3w8a.html.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\dff47ilxk8-u544at3w8a.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3209"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"uZdP155yp3wra21GOF4qDMYiwxN32dAZvXmSFeCE25E=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:42:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u544at3w8a"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-uZdP155yp3wra21GOF4qDMYiwxN32dAZvXmSFeCE25E="}]}, {"Route": "js/app.flty0zdg65.js", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\wqulb7x2k4-flty0zdg65.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000173280194"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5770"}, {"Name": "ETag", "Value": "\"3uIPZLrF8A476VVljjE28lMHu0M76nEEBT6HnFeYKWo=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:42:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "flty0zdg65"}, {"Name": "label", "Value": "js/app.js"}, {"Name": "integrity", "Value": "sha256-Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M="}]}, {"Route": "js/app.flty0zdg65.js", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\js\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30935"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:27:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "flty0zdg65"}, {"Name": "label", "Value": "js/app.js"}, {"Name": "integrity", "Value": "sha256-Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M="}]}, {"Route": "js/app.flty0zdg65.js.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\wqulb7x2k4-flty0zdg65.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5770"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3uIPZLrF8A476VVljjE28lMHu0M76nEEBT6HnFeYKWo=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:42:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "flty0zdg65"}, {"Name": "label", "Value": "js/app.js.gz"}, {"Name": "integrity", "Value": "sha256-3uIPZLrF8A476VVljjE28lMHu0M76nEEBT6HnFeYKWo="}]}, {"Route": "js/app.js", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\wqulb7x2k4-flty0zdg65.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000173280194"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5770"}, {"Name": "ETag", "Value": "\"3uIPZLrF8A476VVljjE28lMHu0M76nEEBT6HnFeYKWo=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:42:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M="}]}, {"Route": "js/app.js", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\wwwroot\\js\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30935"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:27:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M="}]}, {"Route": "js/app.js.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\bot\\CryptoTradingBot\\obj\\Debug\\net9.0\\compressed\\wqulb7x2k4-flty0zdg65.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5770"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3uIPZLrF8A476VVljjE28lMHu0M76nEEBT6HnFeYKWo=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:42:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3uIPZLrF8A476VVljjE28lMHu0M76nEEBT6HnFeYKWo="}]}]}