using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using CryptoTradingBot.Data;
using CryptoTradingBot.Services;
using CryptoTradingBot.Models;

namespace CryptoTradingBot.Controllers
{
    /// <summary>
    /// تحكم في خدمات الذكاء الاصطناعي
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AIController : ControllerBase
    {
        private readonly CryptoTradingDbContext _context;
        private readonly IDeepSeekService _deepSeekService;
        private readonly INewsAnalysisService _newsAnalysisService;
        private readonly ILogger<AIController> _logger;

        public AIController(
            CryptoTradingDbContext context,
            IDeepSeekService deepSeekService,
            INewsAnalysisService newsAnalysisService,
            ILogger<AIController> logger)
        {
            _context = context;
            _deepSeekService = deepSeekService;
            _newsAnalysisService = newsAnalysisService;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على توصية تداول لعملة معينة
        /// </summary>
        [HttpPost("recommendation/{symbol}")]
        public async Task<IActionResult> GetTradingRecommendation(string symbol)
        {
            try
            {
                var currency = await _context.CryptoCurrencies
                    .FirstOrDefaultAsync(c => c.Symbol == symbol && c.IsActive);

                if (currency == null)
                {
                    return NotFound(new { message = "العملة غير موجودة" });
                }

                // جمع البيانات التاريخية
                var priceHistory = await _context.PriceHistories
                    .Where(p => p.CryptoCurrencyId == currency.Id)
                    .OrderByDescending(p => p.Timestamp)
                    .Take(100)
                    .ToListAsync();

                // تحليل المشاعر العام
                var sentimentResult = await _newsAnalysisService.GetOverallSentimentAsync(symbol, 24);

                // الحصول على توصية من DeepSeek
                var recommendation = await _deepSeekService.AnalyzeAndRecommendAsync(
                    symbol, 
                    priceHistory, 
                    sentimentResult);

                return Ok(new
                {
                    symbol,
                    recommendation,
                    sentiment = sentimentResult,
                    dataPoints = priceHistory.Count,
                    generatedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting trading recommendation for {symbol}");
                return StatusCode(500, new { message = "خطأ في الحصول على التوصية" });
            }
        }

        /// <summary>
        /// تحليل المشاعر لنص معين
        /// </summary>
        [HttpPost("sentiment-analysis")]
        public async Task<IActionResult> AnalyzeSentiment([FromBody] SentimentAnalysisRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Text))
                {
                    return BadRequest(new { message = "النص مطلوب" });
                }

                var result = await _deepSeekService.AnalyzeSentimentAsync(request.Text);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error analyzing sentiment");
                return StatusCode(500, new { message = "خطأ في تحليل المشاعر" });
            }
        }

        /// <summary>
        /// التنبؤ بسعر العملة
        /// </summary>
        [HttpPost("price-prediction/{symbol}")]
        public async Task<IActionResult> PredictPrice(string symbol, [FromQuery] string timeframe = "24h")
        {
            try
            {
                var currency = await _context.CryptoCurrencies
                    .FirstOrDefaultAsync(c => c.Symbol == symbol && c.IsActive);

                if (currency == null)
                {
                    return NotFound(new { message = "العملة غير موجودة" });
                }

                var historicalData = await _context.PriceHistories
                    .Where(p => p.CryptoCurrencyId == currency.Id)
                    .OrderByDescending(p => p.Timestamp)
                    .Take(200)
                    .ToListAsync();

                var prediction = await _deepSeekService.PredictPriceAsync(symbol, historicalData, timeframe);
                return Ok(prediction);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error predicting price for {symbol}");
                return StatusCode(500, new { message = "خطأ في التنبؤ بالسعر" });
            }
        }

        /// <summary>
        /// تحسين استراتيجية تداول
        /// </summary>
        [HttpPost("optimize-strategy/{strategyId}")]
        public async Task<IActionResult> OptimizeStrategy(int strategyId)
        {
            try
            {
                var strategy = await _context.TradingStrategies
                    .Include(s => s.StrategyBacktests)
                    .FirstOrDefaultAsync(s => s.Id == strategyId);

                if (strategy == null)
                {
                    return NotFound(new { message = "الاستراتيجية غير موجودة" });
                }

                var performanceData = new
                {
                    strategy.TotalTrades,
                    strategy.WinningTrades,
                    strategy.LosingTrades,
                    strategy.TotalProfit,
                    strategy.WinRate,
                    strategy.AverageReturn,
                    BacktestResults = strategy.StrategyBacktests.OrderByDescending(b => b.CreatedAt).Take(5)
                };

                var optimization = await _deepSeekService.OptimizeStrategyAsync(strategyId, performanceData);
                return Ok(optimization);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error optimizing strategy {strategyId}");
                return StatusCode(500, new { message = "خطأ في تحسين الاستراتيجية" });
            }
        }

        /// <summary>
        /// تحليل شامل للسوق
        /// </summary>
        [HttpGet("market-analysis")]
        public async Task<IActionResult> GetMarketAnalysis()
        {
            try
            {
                var currencies = await _context.CryptoCurrencies
                    .Where(c => c.IsActive)
                    .Take(5) // أهم 5 عملات
                    .ToListAsync();

                var marketAnalysis = new List<object>();

                foreach (var currency in currencies)
                {
                    var priceHistory = await _context.PriceHistories
                        .Where(p => p.CryptoCurrencyId == currency.Id)
                        .OrderByDescending(p => p.Timestamp)
                        .Take(50)
                        .ToListAsync();

                    var sentiment = await _newsAnalysisService.GetOverallSentimentAsync(currency.Symbol, 24);
                    var recommendation = await _deepSeekService.AnalyzeAndRecommendAsync(currency.Symbol, priceHistory, sentiment);

                    marketAnalysis.Add(new
                    {
                        currency.Symbol,
                        currency.Name,
                        currency.CurrentPrice,
                        currency.PriceChange24h,
                        sentiment = sentiment.OverallLabel,
                        sentimentScore = sentiment.AverageSentiment,
                        recommendation = recommendation.Action,
                        confidence = recommendation.Confidence
                    });
                }

                return Ok(new
                {
                    analysis = marketAnalysis,
                    generatedAt = DateTime.UtcNow,
                    summary = new
                    {
                        bullishCount = marketAnalysis.Count(a => ((dynamic)a).recommendation == "BUY"),
                        bearishCount = marketAnalysis.Count(a => ((dynamic)a).recommendation == "SELL"),
                        neutralCount = marketAnalysis.Count(a => ((dynamic)a).recommendation == "HOLD")
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting market analysis");
                return StatusCode(500, new { message = "خطأ في تحليل السوق" });
            }
        }

        /// <summary>
        /// الحصول على تحليل المشاعر العام للعملة
        /// </summary>
        [HttpGet("sentiment/{symbol}")]
        public async Task<IActionResult> GetOverallSentiment(string symbol, [FromQuery] int hours = 24)
        {
            try
            {
                var result = await _newsAnalysisService.GetOverallSentimentAsync(symbol, hours);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting sentiment for {symbol}");
                return StatusCode(500, new { message = "خطأ في تحليل المشاعر" });
            }
        }

        /// <summary>
        /// تحليل خبر معين
        /// </summary>
        [HttpPost("analyze-news/{newsId}")]
        public async Task<IActionResult> AnalyzeNews(long newsId)
        {
            try
            {
                var news = await _context.NewsData.FindAsync(newsId);
                if (news == null)
                {
                    return NotFound(new { message = "الخبر غير موجود" });
                }

                var analysis = await _newsAnalysisService.AnalyzeNewsAsync(news);
                return Ok(analysis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error analyzing news {newsId}");
                return StatusCode(500, new { message = "خطأ في تحليل الخبر" });
            }
        }
    }

    /// <summary>
    /// نموذج طلب تحليل المشاعر
    /// </summary>
    public class SentimentAnalysisRequest
    {
        public string Text { get; set; } = string.Empty;
    }
}
