{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/style.87naruipz6.css", "AssetFile": "css/style.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000577367206"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1731"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DUTSSMRlkapzO+15WZpafVKmgmp/dkqddJHF9iLHyGU=\""}, {"Name": "ETag", "Value": "W/\"DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:52:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87naruipz6"}, {"Name": "integrity", "Value": "sha256-DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs="}, {"Name": "label", "Value": "css/style.css"}]}, {"Route": "css/style.87naruipz6.css", "AssetFile": "css/style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5938"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 16:51:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87naruipz6"}, {"Name": "integrity", "Value": "sha256-DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs="}, {"Name": "label", "Value": "css/style.css"}]}, {"Route": "css/style.87naruipz6.css.gz", "AssetFile": "css/style.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1731"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DUTSSMRlkapzO+15WZpafVKmgmp/dkqddJHF9iLHyGU=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:52:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87naruipz6"}, {"Name": "integrity", "Value": "sha256-DUTSSMRlkapzO+15WZpafVKmgmp/dkqddJHF9iLHyGU="}, {"Name": "label", "Value": "css/style.css.gz"}]}, {"Route": "css/style.css", "AssetFile": "css/style.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000577367206"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1731"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DUTSSMRlkapzO+15WZpafVKmgmp/dkqddJHF9iLHyGU=\""}, {"Name": "ETag", "Value": "W/\"DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:52:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs="}]}, {"Route": "css/style.css", "AssetFile": "css/style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5938"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 16:51:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DNkmN5P/+b7zbg+t1ax4fa59gnB6rRZKjpJiqCG0aKs="}]}, {"Route": "css/style.css.gz", "AssetFile": "css/style.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1731"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DUTSSMRlkapzO+15WZpafVKmgmp/dkqddJHF9iLHyGU=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:52:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DUTSSMRlkapzO+15WZpafVKmgmp/dkqddJHF9iLHyGU="}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000311526480"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3209"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"uZdP155yp3wra21GOF4qDMYiwxN32dAZvXmSFeCE25E=\""}, {"Name": "ETag", "Value": "W/\"Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:52:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22216"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:26:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3209"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"uZdP155yp3wra21GOF4qDMYiwxN32dAZvXmSFeCE25E=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:52:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uZdP155yp3wra21GOF4qDMYiwxN32dAZvXmSFeCE25E="}]}, {"Route": "index.u544at3w8a.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000311526480"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3209"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"uZdP155yp3wra21GOF4qDMYiwxN32dAZvXmSFeCE25E=\""}, {"Name": "ETag", "Value": "W/\"Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:52:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u544at3w8a"}, {"Name": "integrity", "Value": "sha256-Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.u544at3w8a.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22216"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:26:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u544at3w8a"}, {"Name": "integrity", "Value": "sha256-Evw8jdz7GmdLtTBZD04VAEBwE4U9E3nNYYtg/YdOxdA="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.u544at3w8a.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3209"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"uZdP155yp3wra21GOF4qDMYiwxN32dAZvXmSFeCE25E=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:52:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u544at3w8a"}, {"Name": "integrity", "Value": "sha256-uZdP155yp3wra21GOF4qDMYiwxN32dAZvXmSFeCE25E="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "js/app.flty0zdg65.js", "AssetFile": "js/app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000173280194"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5770"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3uIPZLrF8A476VVljjE28lMHu0M76nEEBT6HnFeYKWo=\""}, {"Name": "ETag", "Value": "W/\"Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:52:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "flty0zdg65"}, {"Name": "integrity", "Value": "sha256-Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M="}, {"Name": "label", "Value": "js/app.js"}]}, {"Route": "js/app.flty0zdg65.js", "AssetFile": "js/app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "30935"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:27:13 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "flty0zdg65"}, {"Name": "integrity", "Value": "sha256-Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M="}, {"Name": "label", "Value": "js/app.js"}]}, {"Route": "js/app.flty0zdg65.js.gz", "AssetFile": "js/app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5770"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3uIPZLrF8A476VVljjE28lMHu0M76nEEBT6HnFeYKWo=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:52:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "flty0zdg65"}, {"Name": "integrity", "Value": "sha256-3uIPZLrF8A476VVljjE28lMHu0M76nEEBT6HnFeYKWo="}, {"Name": "label", "Value": "js/app.js.gz"}]}, {"Route": "js/app.js", "AssetFile": "js/app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000173280194"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5770"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3uIPZLrF8A476VVljjE28lMHu0M76nEEBT6HnFeYKWo=\""}, {"Name": "ETag", "Value": "W/\"Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:52:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M="}]}, {"Route": "js/app.js", "AssetFile": "js/app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "30935"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:27:13 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Uwb/d8PhHB13qSw18Gxvuc6cAZQnnIQHS+tNjlltL4M="}]}, {"Route": "js/app.js.gz", "AssetFile": "js/app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5770"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3uIPZLrF8A476VVljjE28lMHu0M76nEEBT6HnFeYKWo=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:52:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3uIPZLrF8A476VVljjE28lMHu0M76nEEBT6HnFeYKWo="}]}, {"Route": "test.html", "AssetFile": "test.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000409165303"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2443"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Mey7HQ39l4ClnQUG/jLuoBnPyMfvuHRYrJ7fp56sZTw=\""}, {"Name": "ETag", "Value": "W/\"PCLDkTN7OPADiD8ua3c5uB1vazvKxp3CVaJDFuVizfo=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:52:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PCLDkTN7OPADiD8ua3c5uB1vazvKxp3CVaJDFuVizfo="}]}, {"Route": "test.html", "AssetFile": "test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8714"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"PCLDkTN7OPADiD8ua3c5uB1vazvKxp3CVaJDFuVizfo=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:49:04 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PCLDkTN7OPADiD8ua3c5uB1vazvKxp3CVaJDFuVizfo="}]}, {"Route": "test.html.gz", "AssetFile": "test.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2443"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Mey7HQ39l4ClnQUG/jLuoBnPyMfvuHRYrJ7fp56sZTw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:52:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Mey7HQ39l4ClnQUG/jLuoBnPyMfvuHRYrJ7fp56sZTw="}]}, {"Route": "test.sogzba1yik.html", "AssetFile": "test.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000409165303"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2443"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Mey7HQ39l4ClnQUG/jLuoBnPyMfvuHRYrJ7fp56sZTw=\""}, {"Name": "ETag", "Value": "W/\"PCLDkTN7OPADiD8ua3c5uB1vazvKxp3CVaJDFuVizfo=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:52:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sogzba1yik"}, {"Name": "integrity", "Value": "sha256-PCLDkTN7OPADiD8ua3c5uB1vazvKxp3CVaJDFuVizfo="}, {"Name": "label", "Value": "test.html"}]}, {"Route": "test.sogzba1yik.html", "AssetFile": "test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8714"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"PCLDkTN7OPADiD8ua3c5uB1vazvKxp3CVaJDFuVizfo=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 17:49:04 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sogzba1yik"}, {"Name": "integrity", "Value": "sha256-PCLDkTN7OPADiD8ua3c5uB1vazvKxp3CVaJDFuVizfo="}, {"Name": "label", "Value": "test.html"}]}, {"Route": "test.sogzba1yik.html.gz", "AssetFile": "test.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2443"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Mey7HQ39l4ClnQUG/jLuoBnPyMfvuHRYrJ7fp56sZTw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:52:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sogzba1yik"}, {"Name": "integrity", "Value": "sha256-Mey7HQ39l4ClnQUG/jLuoBnPyMfvuHRYrJ7fp56sZTw="}, {"Name": "label", "Value": "test.html.gz"}]}]}