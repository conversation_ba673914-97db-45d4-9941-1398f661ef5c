{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=cryptobot;Username=postgres;Password=password", "Redis": "localhost:6379"}, "Jwt": {"Key": "CryptoTradingBotSecretKey123456789012345678901234567890", "Issuer": "CryptoTradingBot", "Audience": "CryptoTradingBot"}, "Encryption": {"Key": "CryptoTradingBotEncryptionKey123456"}, "Security": {"Salt": "CryptoTradingBotSalt123"}, "ApiSettings": {"CoinGecko": {"BaseUrl": "https://api.coingecko.com/api/v3", "RateLimit": 30}, "AlphaVantage": {"BaseUrl": "https://www.alphavantage.co/query", "RateLimit": 25}, "DeepSeek": {"BaseUrl": "https://api.deepseek.com/v1", "Model": "deepseek-chat"}}, "TradingSettings": {"DefaultMode": "PAPER", "MaxConcurrentTrades": 5, "DefaultStopLoss": 5.0, "DefaultTakeProfit": 10.0, "MinTradeAmount": 10.0, "MaxTradeAmount": 1000.0}}