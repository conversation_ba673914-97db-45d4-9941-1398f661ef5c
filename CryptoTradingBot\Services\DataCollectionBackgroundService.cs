using CryptoTradingBot.Data;
using Microsoft.EntityFrameworkCore;

namespace CryptoTradingBot.Services
{
    /// <summary>
    /// خدمة خلفية لجمع البيانات بشكل دوري
    /// </summary>
    public class DataCollectionBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<DataCollectionBackgroundService> _logger;
        private readonly IConfiguration _configuration;

        public DataCollectionBackgroundService(
            IServiceProvider serviceProvider,
            ILogger<DataCollectionBackgroundService> logger,
            IConfiguration configuration)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _configuration = configuration;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Data Collection Background Service started");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await CollectDataAsync();
                    
                    // انتظار 5 دقائق قبل التحديث التالي
                    await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("Data Collection Background Service is stopping");
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in Data Collection Background Service");
                    
                    // انتظار دقيقة واحدة في حالة الخطأ
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
            }
        }

        private async Task CollectDataAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<CryptoTradingDbContext>();
            var dataCollectionService = scope.ServiceProvider.GetRequiredService<IDataCollectionService>();
            var newsAnalysisService = scope.ServiceProvider.GetRequiredService<INewsAnalysisService>();

            try
            {
                _logger.LogInformation("Starting data collection cycle");

                // جمع الأسعار الحالية
                await CollectCurrentPrices(context, dataCollectionService);

                // جمع البيانات التاريخية (مرة واحدة في الساعة)
                if (DateTime.UtcNow.Minute < 5) // في أول 5 دقائق من كل ساعة
                {
                    await CollectHistoricalData(context, dataCollectionService);
                }

                // جمع الأخبار (كل 15 دقيقة)
                if (DateTime.UtcNow.Minute % 15 == 0)
                {
                    await CollectNewsData(context, dataCollectionService, newsAnalysisService);
                }

                _logger.LogInformation("Data collection cycle completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during data collection cycle");
            }
        }

        private async Task CollectCurrentPrices(CryptoTradingDbContext context, IDataCollectionService dataCollectionService)
        {
            try
            {
                // الحصول على قائمة العملات النشطة
                var activeCurrencies = await context.CryptoCurrencies
                    .Where(c => c.IsActive)
                    .Select(c => c.Symbol)
                    .ToListAsync();

                if (activeCurrencies.Any())
                {
                    var updatedCurrencies = await dataCollectionService.GetCurrentPricesAsync(activeCurrencies);
                    
                    if (updatedCurrencies.Any())
                    {
                        await dataCollectionService.UpdateCurrenciesAsync(updatedCurrencies);
                        _logger.LogInformation($"Updated prices for {updatedCurrencies.Count} currencies");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error collecting current prices");
            }
        }

        private async Task CollectHistoricalData(CryptoTradingDbContext context, IDataCollectionService dataCollectionService)
        {
            try
            {
                var currencies = await context.CryptoCurrencies
                    .Where(c => c.IsActive)
                    .ToListAsync();

                foreach (var currency in currencies)
                {
                    // جمع البيانات التاريخية لآخر يوم
                    var historicalData = await dataCollectionService.GetHistoricalPricesAsync(currency.Symbol, 1, "1h");
                    
                    if (historicalData.Any())
                    {
                        await dataCollectionService.SavePriceHistoriesAsync(historicalData);
                        _logger.LogInformation($"Saved {historicalData.Count} historical price points for {currency.Symbol}");
                    }

                    // تأخير قصير لتجنب تجاوز حدود API
                    await Task.Delay(1000);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error collecting historical data");
            }
        }

        private async Task CollectNewsData(CryptoTradingDbContext context, IDataCollectionService dataCollectionService, INewsAnalysisService newsAnalysisService)
        {
            try
            {
                // كلمات مفتاحية للبحث عن الأخبار
                var keywords = new List<string> { "bitcoin", "ethereum", "cryptocurrency", "blockchain", "defi" };
                
                var newsData = await dataCollectionService.GetCryptoNewsAsync(keywords, 1); // آخر ساعة
                
                if (newsData.Any())
                {
                    await dataCollectionService.SaveNewsDataAsync(newsData);
                    _logger.LogInformation($"Collected {newsData.Count} news articles");

                    // تحليل الأخبار الجديدة
                    foreach (var news in newsData.Where(n => !n.IsProcessed))
                    {
                        try
                        {
                            await newsAnalysisService.AnalyzeNewsAsync(news);
                            await Task.Delay(500); // تأخير لتجنب الحمولة الزائدة
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"Error analyzing news {news.Id}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error collecting news data");
            }
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Data Collection Background Service is stopping");
            await base.StopAsync(stoppingToken);
        }
    }
}
