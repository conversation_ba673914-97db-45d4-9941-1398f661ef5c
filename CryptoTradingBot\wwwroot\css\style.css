/* تصميم مخصص لبوت تداول العملات المشفرة */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    direction: rtl;
}

/* شريط التنقل */
.navbar-brand {
    font-weight: bold;
    font-size: 1.2rem;
}

.navbar-nav .nav-link {
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* بطاقات الإحصائيات */
.card.bg-primary,
.card.bg-success,
.card.bg-info,
.card.bg-warning {
    border-radius: 15px;
    margin-bottom: 20px;
}

.card.bg-primary .card-body,
.card.bg-success .card-body,
.card.bg-info .card-body,
.card.bg-warning .card-body {
    padding: 1.5rem;
}

.card.bg-primary .card-body h4,
.card.bg-success .card-body h4,
.card.bg-info .card-body h4,
.card.bg-warning .card-body h4 {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border: none;
    font-weight: 600;
    color: #495057;
}

.table tbody tr {
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
    border: none;
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
    border: none;
}

.btn-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    border: none;
    color: #212529;
}

/* النماذج */
.form-control,
.form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* المودال */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 15px 15px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 15px 15px;
}

/* سجل الأنشطة */
#activity-log {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    padding: 10px;
    border-left: 3px solid #007bff;
    margin-bottom: 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.activity-item.success {
    border-left-color: #28a745;
}

.activity-item.warning {
    border-left-color: #ffc107;
}

.activity-item.error {
    border-left-color: #dc3545;
}

/* تكوين APIs */
.api-config-item {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: #fff;
    transition: all 0.3s ease;
}

.api-config-item:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.api-config-item .api-name {
    font-weight: 600;
    font-size: 1.1rem;
    color: #495057;
}

.api-config-item .api-status {
    font-size: 0.9rem;
}

.api-config-item .api-status.active {
    color: #28a745;
}

.api-config-item .api-status.inactive {
    color: #dc3545;
}

/* مؤشرات الحالة */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-left: 5px;
}

.status-indicator.online {
    background-color: #28a745;
}

.status-indicator.offline {
    background-color: #dc3545;
}

.status-indicator.pending {
    background-color: #ffc107;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .card.bg-primary,
    .card.bg-success,
    .card.bg-info,
    .card.bg-warning {
        margin-bottom: 15px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
    
    .btn {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* تحسينات إضافية */
.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.bg-light-success {
    background-color: rgba(40, 167, 69, 0.1) !important;
}

.bg-light-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
}

.bg-light-warning {
    background-color: rgba(255, 193, 7, 0.1) !important;
}

/* تحسين المسافات */
.mt-4 {
    margin-top: 1.5rem !important;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.p-3 {
    padding: 1rem !important;
}

/* تحسين الخطوط */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: #343a40;
}

small {
    color: #6c757d;
}

/* تحسين الروابط */
a {
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    text-decoration: none;
}

/* تحسين الحدود */
.border-radius-10 {
    border-radius: 10px !important;
}

.border-radius-15 {
    border-radius: 15px !important;
}

/* تحسين الظلال */
.shadow-sm {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.shadow {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2) !important;
}
