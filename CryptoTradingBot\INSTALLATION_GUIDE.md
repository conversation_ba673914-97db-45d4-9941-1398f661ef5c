# 📦 دليل التثبيت - CryptoTradingBot مع Docker Compose

## 🎯 **المتطلبات الأساسية**

### **1. Docker Desktop (مطلوب)**
```bash
# تحميل وتثبيت Docker Desktop من:
# https://www.docker.com/products/docker-desktop/

# للتحقق من التثبيت:
docker --version
docker-compose --version
```

### **2. Git (مطلوب)**
```bash
# تحميل من: https://git-scm.com/downloads
git --version
```

### **3. Make (اختياري - لتسهيل الإدارة)**
```bash
# Windows: تثبيت من خلال Chocolatey
choco install make

# أو استخدام WSL2
wsl --install
```

---

## 🚀 **طرق التشغيل**

### **الطريقة 1: باستخدام Make (الأسهل)**

#### **1. الإعداد الأولي:**
```bash
# إنشاء المجلدات المطلوبة
make setup

# تحرير ملف البيئة
cp .env.example .env
# قم بتحرير .env وإضافة API Keys الخاصة بك
```

#### **2. البدء السريع:**
```bash
# تشغيل بيئة التطوير كاملة
make quick-start
```

#### **3. الوصول للتطبيق:**
- **التطبيق الرئيسي:** http://localhost:5168
- **Grafana:** http://localhost:3000 (admin/admin123)
- **Prometheus:** http://localhost:9091

### **الطريقة 2: باستخدام Docker Compose مباشرة**

#### **1. الإعداد:**
```bash
# إنشاء المجلدات
mkdir -p data logs nginx/logs grafana/dashboards grafana/datasources

# نسخ ملف البيئة
cp .env.example .env
# قم بتحرير .env
```

#### **2. تشغيل التطوير:**
```bash
# بناء الصور
docker-compose -f docker-compose.yml -f docker-compose.override.yml build

# تشغيل الخدمات
docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d

# عرض السجلات
docker-compose -f docker-compose.yml -f docker-compose.override.yml logs -f
```

#### **3. تشغيل الإنتاج:**
```bash
# بناء للإنتاج
docker-compose -f docker-compose.yml -f docker-compose.prod.yml build

# تشغيل الإنتاج
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### **الطريقة 3: تشغيل محلي (بدون Docker)**

#### **1. متطلبات .NET:**
```bash
# تثبيت .NET 9 SDK
# https://dotnet.microsoft.com/download/dotnet/9.0

dotnet --version
```

#### **2. تشغيل التطبيق:**
```bash
# استعادة الحزم
dotnet restore

# بناء المشروع
dotnet build

# تشغيل التطبيق
dotnet run
```

---

## ⚙️ **إعداد متغيرات البيئة**

### **ملف .env المطلوب:**
```env
# API Keys (احصل عليها من المواقع الرسمية)
DEEPSEEK_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
NEWSAPI_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
COINGECKO_API_KEY=CG-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Security (غير هذه القيم)
JWT_SECRET_KEY=your_super_secret_jwt_key_minimum_32_characters
ENCRYPTION_KEY=your_32_character_encryption_key

# Database
DATABASE_PATH=/app/data/cryptobot.db

# Redis
REDIS_CONNECTION=redis:6379

# Grafana
GRAFANA_ADMIN_PASSWORD=your_secure_password
```

### **الحصول على API Keys:**

#### **1. DeepSeek AI:**
- اذهب إلى: https://platform.deepseek.com/
- سجل حساب جديد
- احصل على API Key

#### **2. NewsAPI:**
- اذهب إلى: https://newsapi.org/
- سجل حساب مجاني
- احصل على API Key

#### **3. CoinGecko:**
- اذهب إلى: https://www.coingecko.com/en/api
- سجل حساب (اختياري للاستخدام المجاني)
- احصل على API Key

---

## 🔧 **استكشاف الأخطاء**

### **مشاكل شائعة:**

#### **1. Docker غير مثبت:**
```bash
# خطأ: 'docker' is not recognized
# الحل: تثبيت Docker Desktop
```

#### **2. منافذ مشغولة:**
```bash
# خطأ: Port already in use
# الحل: تغيير المنافذ في docker-compose.override.yml
ports:
  - "5169:80"  # بدلاً من 5168
```

#### **3. مشاكل الأذونات:**
```bash
# خطأ: Permission denied
# الحل: تشغيل PowerShell كمدير
```

#### **4. مشاكل الذاكرة:**
```bash
# خطأ: Out of memory
# الحل: زيادة ذاكرة Docker Desktop
# Settings > Resources > Memory > 4GB+
```

### **أوامر التشخيص:**
```bash
# فحص حالة الخدمات
docker-compose ps

# فحص السجلات
docker-compose logs cryptotradingbot

# فحص استخدام الموارد
docker stats

# تنظيف الموارد
docker system prune -f
```

---

## 📊 **التحقق من التثبيت**

### **1. فحص الخدمات:**
```bash
# فحص Docker
docker --version
docker-compose --version

# فحص الخدمات
curl http://localhost:5168/health
curl http://localhost:3000  # Grafana
curl http://localhost:9091  # Prometheus
```

### **2. فحص قاعدة البيانات:**
```bash
# دخول إلى container التطبيق
docker-compose exec cryptotradingbot /bin/bash

# فحص قاعدة البيانات
ls -la /app/data/
```

### **3. فحص Redis:**
```bash
# دخول إلى Redis CLI
docker-compose exec redis redis-cli

# داخل Redis:
ping
info
keys *
```

---

## 🎯 **الخطوات التالية**

### **بعد التثبيت الناجح:**

1. **تسجيل الدخول:**
   - اذهب إلى: http://localhost:5168
   - المستخدم: `admin`
   - كلمة المرور: `admin123`

2. **إعداد APIs:**
   - اذهب إلى قسم "إعدادات APIs"
   - أدخل API Keys الخاصة بك

3. **مراقبة النظام:**
   - Grafana: http://localhost:3000
   - Prometheus: http://localhost:9091

4. **بدء التداول:**
   - اختر وضع Paper Trading للاختبار
   - قم بإعداد استراتيجيات التداول

---

## 📞 **الدعم والمساعدة**

### **إذا واجهت مشاكل:**

1. **راجع السجلات:**
   ```bash
   make logs
   # أو
   docker-compose logs -f
   ```

2. **فحص الصحة:**
   ```bash
   make health
   ```

3. **إعادة التشغيل:**
   ```bash
   make restart
   ```

4. **تنظيف وإعادة البناء:**
   ```bash
   make clean
   make dev-build
   make dev-up
   ```

### **للمساعدة الإضافية:**
- راجع `DOCKER_README.md` للتفاصيل الكاملة
- راجع `PROJECT_VERIFICATION_REPORT.md` للتحقق من المكونات
- استخدم `make help` لعرض جميع الأوامر المتاحة

---

**🎉 مبروك! تطبيق CryptoTradingBot جاهز للاستخدام مع Docker Compose!**
