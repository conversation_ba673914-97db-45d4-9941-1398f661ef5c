using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CryptoTradingBot.Models
{
    /// <summary>
    /// نموذج بيانات الأخبار وتحليل المشاعر
    /// </summary>
    public class NewsData
    {
        [Key]
        public long Id { get; set; }

        [Required]
        [StringLength(500)]
        public string Title { get; set; } = string.Empty;

        [Column(TypeName = "text")]
        public string? Content { get; set; }

        [StringLength(1000)]
        public string? Summary { get; set; }

        [StringLength(500)]
        public string? Url { get; set; }

        [StringLength(100)]
        public string? Source { get; set; }

        [StringLength(100)]
        public string? Author { get; set; }

        public DateTime PublishedAt { get; set; }

        [Column(TypeName = "decimal(3,2)")]
        public decimal SentimentScore { get; set; } // -1.0 to 1.0

        [StringLength(20)]
        public string SentimentLabel { get; set; } = "NEUTRAL"; // POSITIVE, NEGATIVE, NEUTRAL

        [Column(TypeName = "decimal(3,2)")]
        public decimal Confidence { get; set; } // 0.0 to 1.0

        [StringLength(200)]
        public string? Keywords { get; set; }

        [StringLength(100)]
        public string? Category { get; set; }

        public bool IsProcessed { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // العلاقات مع العملات المشفرة المذكورة في الخبر
        public virtual ICollection<NewsCryptoCurrency> NewsCryptoCurrencies { get; set; } = new List<NewsCryptoCurrency>();
    }

    /// <summary>
    /// جدول ربط بين الأخبار والعملات المشفرة
    /// </summary>
    public class NewsCryptoCurrency
    {
        [Key]
        public long Id { get; set; }

        public long NewsDataId { get; set; }
        public int CryptoCurrencyId { get; set; }

        [Column(TypeName = "decimal(3,2)")]
        public decimal Relevance { get; set; } // 0.0 to 1.0

        // العلاقات
        [ForeignKey("NewsDataId")]
        public virtual NewsData NewsData { get; set; } = null!;

        [ForeignKey("CryptoCurrencyId")]
        public virtual CryptoCurrency CryptoCurrency { get; set; } = null!;
    }
}
