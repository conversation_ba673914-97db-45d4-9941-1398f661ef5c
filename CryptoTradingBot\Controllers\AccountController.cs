using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using CryptoTradingBot.Data;
using CryptoTradingBot.Models;
using CryptoTradingBot.Services;
using System.Security.Claims;

namespace CryptoTradingBot.Controllers
{
    /// <summary>
    /// تحكم في إدارة الحسابات والمصادقة
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AccountController : ControllerBase
    {
        private readonly CryptoTradingDbContext _context;
        private readonly IEncryptionService _encryptionService;
        private readonly ILogger<AccountController> _logger;

        public AccountController(
            CryptoTradingDbContext context,
            IEncryptionService encryptionService,
            ILogger<AccountController> logger)
        {
            _context = context;
            _encryptionService = encryptionService;
            _logger = logger;
        }

        /// <summary>
        /// تسجيل الدخول
        /// </summary>
        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            try
            {
                // للتبسيط، سنستخدم مصادقة أساسية
                // في الإنتاج، يجب استخدام نظام مصادقة أكثر تعقيداً
                if (request.Username == "admin" && request.Password == "admin123")
                {
                    var token = _encryptionService.GenerateJwtToken("admin", "<EMAIL>");
                    
                    await LogActivity("LOGIN", "User", "admin", "Successful login");
                    
                    return Ok(new { token, message = "تم تسجيل الدخول بنجاح" });
                }

                await LogActivity("LOGIN_FAILED", "User", request.Username, "Failed login attempt");
                return Unauthorized(new { message = "بيانات الدخول غير صحيحة" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login");
                return StatusCode(500, new { message = "خطأ في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على إعدادات المستخدم
        /// </summary>
        [HttpGet("settings")]
        [Authorize]
        public async Task<IActionResult> GetUserSettings()
        {
            try
            {
                var userId = GetCurrentUserId();
                var settings = await _context.UserSettings
                    .Include(s => s.ApiConfigurations)
                    .FirstOrDefaultAsync(s => s.UserId == userId);

                if (settings == null)
                {
                    // إنشاء إعدادات افتراضية
                    settings = new UserSettings { UserId = userId };
                    _context.UserSettings.Add(settings);
                    await _context.SaveChangesAsync();
                }

                // فك تشفير API Keys للعرض (إخفاء جزء منها للأمان)
                foreach (var apiConfig in settings.ApiConfigurations)
                {
                    if (!string.IsNullOrEmpty(apiConfig.ApiKey))
                    {
                        var decrypted = _encryptionService.Decrypt(apiConfig.ApiKey);
                        apiConfig.ApiKey = MaskApiKey(decrypted);
                    }
                    if (!string.IsNullOrEmpty(apiConfig.ApiSecret))
                    {
                        apiConfig.ApiSecret = "***HIDDEN***";
                    }
                }

                return Ok(settings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user settings");
                return StatusCode(500, new { message = "خطأ في جلب الإعدادات" });
            }
        }

        /// <summary>
        /// تحديث إعدادات المستخدم
        /// </summary>
        [HttpPut("settings")]
        [Authorize]
        public async Task<IActionResult> UpdateUserSettings([FromBody] UserSettings updatedSettings)
        {
            try
            {
                var userId = GetCurrentUserId();
                var settings = await _context.UserSettings
                    .FirstOrDefaultAsync(s => s.UserId == userId);

                if (settings == null)
                {
                    updatedSettings.UserId = userId;
                    _context.UserSettings.Add(updatedSettings);
                }
                else
                {
                    settings.TradingMode = updatedSettings.TradingMode;
                    settings.MaxTotalInvestment = updatedSettings.MaxTotalInvestment;
                    settings.MaxInvestmentPerTrade = updatedSettings.MaxInvestmentPerTrade;
                    settings.RiskLevel = updatedSettings.RiskLevel;
                    settings.GlobalStopLoss = updatedSettings.GlobalStopLoss;
                    settings.GlobalTakeProfit = updatedSettings.GlobalTakeProfit;
                    settings.AutoTradingEnabled = updatedSettings.AutoTradingEnabled;
                    settings.NotificationsEnabled = updatedSettings.NotificationsEnabled;
                    settings.EmailNotifications = updatedSettings.EmailNotifications;
                    settings.PhoneNotifications = updatedSettings.PhoneNotifications;
                    settings.PreferredCurrencies = updatedSettings.PreferredCurrencies;
                    settings.MaxConcurrentTrades = updatedSettings.MaxConcurrentTrades;
                    settings.UpdatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();
                await LogActivity("UPDATE_SETTINGS", "UserSettings", userId, "Settings updated");

                return Ok(new { message = "تم تحديث الإعدادات بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user settings");
                return StatusCode(500, new { message = "خطأ في تحديث الإعدادات" });
            }
        }

        /// <summary>
        /// إضافة أو تحديث تكوين API
        /// </summary>
        [HttpPost("api-config")]
        [Authorize]
        public async Task<IActionResult> SaveApiConfiguration([FromBody] ApiConfigurationRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                var userSettings = await _context.UserSettings
                    .Include(s => s.ApiConfigurations)
                    .FirstOrDefaultAsync(s => s.UserId == userId);

                if (userSettings == null)
                {
                    return BadRequest(new { message = "إعدادات المستخدم غير موجودة" });
                }

                var existingConfig = userSettings.ApiConfigurations
                    .FirstOrDefault(c => c.ApiName == request.ApiName);

                if (existingConfig != null)
                {
                    // تحديث التكوين الموجود
                    existingConfig.ApiKey = _encryptionService.Encrypt(request.ApiKey);
                    if (!string.IsNullOrEmpty(request.ApiSecret))
                    {
                        existingConfig.ApiSecret = _encryptionService.Encrypt(request.ApiSecret);
                    }
                    existingConfig.BaseUrl = request.BaseUrl;
                    existingConfig.IsActive = request.IsActive;
                    existingConfig.IsTestnet = request.IsTestnet;
                    existingConfig.AdditionalSettings = request.AdditionalSettings;
                    existingConfig.UpdatedAt = DateTime.UtcNow;
                }
                else
                {
                    // إضافة تكوين جديد
                    var newConfig = new ApiConfiguration
                    {
                        UserSettingsId = userSettings.Id,
                        ApiName = request.ApiName,
                        ApiKey = _encryptionService.Encrypt(request.ApiKey),
                        ApiSecret = !string.IsNullOrEmpty(request.ApiSecret) 
                            ? _encryptionService.Encrypt(request.ApiSecret) : null,
                        BaseUrl = request.BaseUrl,
                        IsActive = request.IsActive,
                        IsTestnet = request.IsTestnet,
                        AdditionalSettings = request.AdditionalSettings
                    };
                    _context.ApiConfigurations.Add(newConfig);
                }

                await _context.SaveChangesAsync();
                await LogActivity("SAVE_API_CONFIG", "ApiConfiguration", request.ApiName, 
                    $"API configuration saved for {request.ApiName}");

                return Ok(new { message = "تم حفظ تكوين API بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving API configuration");
                return StatusCode(500, new { message = "خطأ في حفظ تكوين API" });
            }
        }

        /// <summary>
        /// حذف تكوين API
        /// </summary>
        [HttpDelete("api-config/{apiName}")]
        [Authorize]
        public async Task<IActionResult> DeleteApiConfiguration(string apiName)
        {
            try
            {
                var userId = GetCurrentUserId();
                var userSettings = await _context.UserSettings
                    .Include(s => s.ApiConfigurations)
                    .FirstOrDefaultAsync(s => s.UserId == userId);

                if (userSettings == null)
                {
                    return BadRequest(new { message = "إعدادات المستخدم غير موجودة" });
                }

                var config = userSettings.ApiConfigurations
                    .FirstOrDefault(c => c.ApiName == apiName);

                if (config == null)
                {
                    return NotFound(new { message = "تكوين API غير موجود" });
                }

                _context.ApiConfigurations.Remove(config);
                await _context.SaveChangesAsync();
                await LogActivity("DELETE_API_CONFIG", "ApiConfiguration", apiName, 
                    $"API configuration deleted for {apiName}");

                return Ok(new { message = "تم حذف تكوين API بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting API configuration");
                return StatusCode(500, new { message = "خطأ في حذف تكوين API" });
            }
        }

        /// <summary>
        /// اختبار اتصال API
        /// </summary>
        [HttpPost("test-api/{apiName}")]
        [Authorize]
        public async Task<IActionResult> TestApiConnection(string apiName)
        {
            try
            {
                var userId = GetCurrentUserId();
                var config = await _context.ApiConfigurations
                    .Include(c => c.UserSettings)
                    .FirstOrDefaultAsync(c => c.UserSettings.UserId == userId && c.ApiName == apiName);

                if (config == null)
                {
                    return NotFound(new { message = "تكوين API غير موجود" });
                }

                // هنا يمكن إضافة منطق اختبار الاتصال حسب نوع API
                var isConnected = await TestApiConnectionInternal(config);

                await LogActivity("TEST_API", "ApiConfiguration", apiName, 
                    $"API connection test: {(isConnected ? "Success" : "Failed")}");

                return Ok(new { 
                    isConnected, 
                    message = isConnected ? "الاتصال ناجح" : "فشل في الاتصال" 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing API connection");
                return StatusCode(500, new { message = "خطأ في اختبار الاتصال" });
            }
        }

        private string GetCurrentUserId()
        {
            return User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "default";
        }

        private string MaskApiKey(string apiKey)
        {
            if (string.IsNullOrEmpty(apiKey) || apiKey.Length < 8)
                return "***";
            
            return apiKey.Substring(0, 4) + "***" + apiKey.Substring(apiKey.Length - 4);
        }

        private async Task<bool> TestApiConnectionInternal(ApiConfiguration config)
        {
            // منطق اختبار الاتصال - يمكن تطويره لاحقاً
            await Task.Delay(1000); // محاكاة اختبار الاتصال
            return true;
        }

        private async Task LogActivity(string action, string entity, string entityId, string description)
        {
            var log = new ActivityLog
            {
                UserId = GetCurrentUserId(),
                Action = action,
                Entity = entity,
                EntityId = entityId,
                Description = description,
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString()
            };

            _context.ActivityLogs.Add(log);
            await _context.SaveChangesAsync();
        }
    }

    // نماذج الطلبات
    public class LoginRequest
    {
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }

    public class ApiConfigurationRequest
    {
        public string ApiName { get; set; } = string.Empty;
        public string ApiKey { get; set; } = string.Empty;
        public string? ApiSecret { get; set; }
        public string? BaseUrl { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsTestnet { get; set; } = false;
        public string? AdditionalSettings { get; set; }
    }
}
