# Grafana Datasources Configuration
apiVersion: 1

datasources:
  # Prometheus Data Source
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      httpMethod: POST
      manageAlerts: true
      prometheusType: Prometheus
      prometheusVersion: 2.40.0
      cacheLevel: 'High'
      disableRecordingRules: false
      incrementalQueryOverlapWindow: 10m

  # Redis Data Source (if using redis-datasource plugin)
  - name: Redis
    type: redis-datasource
    access: proxy
    url: redis://redis:6379
    editable: true
    jsonData:
      client: standalone
      poolSize: 5
      timeout: 10
      pingInterval: 0
      pipelineWindow: 0

  # JSON API Data Source for CryptoTradingBot
  - name: CryptoTradingBot API
    type: simplejson
    access: proxy
    url: http://cryptotradingbot:80/api
    editable: true
    jsonData:
      httpMethod: GET
    secureJsonData:
      # Add API key if needed
      # apiKey: your_api_key_here
