using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using CryptoTradingBot.Data;
using CryptoTradingBot.Services;

namespace CryptoTradingBot.Controllers
{
    /// <summary>
    /// تحكم في البيانات والأسعار
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class DataController : ControllerBase
    {
        private readonly CryptoTradingDbContext _context;
        private readonly IDataCollectionService _dataCollectionService;
        private readonly ILogger<DataController> _logger;

        public DataController(
            CryptoTradingDbContext context,
            IDataCollectionService dataCollectionService,
            ILogger<DataController> logger)
        {
            _context = context;
            _dataCollectionService = dataCollectionService;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على الأسعار الحالية
        /// </summary>
        [HttpGet("prices")]
        public async Task<IActionResult> GetCurrentPrices()
        {
            try
            {
                var currencies = await _context.CryptoCurrencies
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.Symbol)
                    .ToListAsync();

                return Ok(currencies);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current prices");
                return StatusCode(500, new { message = "خطأ في جلب الأسعار" });
            }
        }

        /// <summary>
        /// تحديث الأسعار يدوياً
        /// </summary>
        [HttpPost("prices/refresh")]
        public async Task<IActionResult> RefreshPrices()
        {
            try
            {
                var symbols = await _context.CryptoCurrencies
                    .Where(c => c.IsActive)
                    .Select(c => c.Symbol)
                    .ToListAsync();

                var updatedCurrencies = await _dataCollectionService.GetCurrentPricesAsync(symbols);
                
                if (updatedCurrencies.Any())
                {
                    await _dataCollectionService.UpdateCurrenciesAsync(updatedCurrencies);
                    return Ok(new { message = $"تم تحديث {updatedCurrencies.Count} عملة", currencies = updatedCurrencies });
                }

                return Ok(new { message = "لا توجد تحديثات متاحة" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing prices");
                return StatusCode(500, new { message = "خطأ في تحديث الأسعار" });
            }
        }

        /// <summary>
        /// الحصول على البيانات التاريخية
        /// </summary>
        [HttpGet("history/{symbol}")]
        public async Task<IActionResult> GetPriceHistory(string symbol, [FromQuery] int days = 7, [FromQuery] string interval = "1h")
        {
            try
            {
                var currency = await _context.CryptoCurrencies
                    .FirstOrDefaultAsync(c => c.Symbol == symbol && c.IsActive);

                if (currency == null)
                {
                    return NotFound(new { message = "العملة غير موجودة" });
                }

                var history = await _context.PriceHistories
                    .Where(p => p.CryptoCurrencyId == currency.Id && 
                               p.TimeFrame == interval &&
                               p.Timestamp >= DateTime.UtcNow.AddDays(-days))
                    .OrderBy(p => p.Timestamp)
                    .ToListAsync();

                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting price history for {symbol}");
                return StatusCode(500, new { message = "خطأ في جلب البيانات التاريخية" });
            }
        }

        /// <summary>
        /// الحصول على الأخبار الحديثة
        /// </summary>
        [HttpGet("news")]
        public async Task<IActionResult> GetRecentNews([FromQuery] int hours = 24, [FromQuery] int limit = 20)
        {
            try
            {
                var cutoffTime = DateTime.UtcNow.AddHours(-hours);
                
                var news = await _context.NewsData
                    .Where(n => n.PublishedAt >= cutoffTime)
                    .OrderByDescending(n => n.PublishedAt)
                    .Take(limit)
                    .ToListAsync();

                return Ok(news);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent news");
                return StatusCode(500, new { message = "خطأ في جلب الأخبار" });
            }
        }

        /// <summary>
        /// جلب أخبار جديدة
        /// </summary>
        [HttpPost("news/refresh")]
        public async Task<IActionResult> RefreshNews()
        {
            try
            {
                var keywords = new List<string> { "bitcoin", "ethereum", "cryptocurrency", "blockchain" };
                var newsData = await _dataCollectionService.GetCryptoNewsAsync(keywords, 24);

                if (newsData.Any())
                {
                    await _dataCollectionService.SaveNewsDataAsync(newsData);
                    return Ok(new { message = $"تم جلب {newsData.Count} خبر جديد", news = newsData });
                }

                return Ok(new { message = "لا توجد أخبار جديدة" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing news");
                return StatusCode(500, new { message = "خطأ في جلب الأخبار الجديدة" });
            }
        }

        /// <summary>
        /// الحصول على إحصائيات السوق
        /// </summary>
        [HttpGet("market-stats")]
        public async Task<IActionResult> GetMarketStats()
        {
            try
            {
                var currencies = await _context.CryptoCurrencies
                    .Where(c => c.IsActive)
                    .ToListAsync();

                var totalMarketCap = currencies.Sum(c => c.MarketCap);
                var totalVolume = currencies.Sum(c => c.Volume24h);
                var avgPriceChange = currencies.Average(c => c.PriceChange24h);
                
                var positiveChanges = currencies.Count(c => c.PriceChange24h > 0);
                var negativeChanges = currencies.Count(c => c.PriceChange24h < 0);

                var stats = new
                {
                    TotalMarketCap = totalMarketCap,
                    TotalVolume24h = totalVolume,
                    AveragePriceChange24h = avgPriceChange,
                    CurrenciesCount = currencies.Count,
                    PositiveChanges = positiveChanges,
                    NegativeChanges = negativeChanges,
                    LastUpdated = currencies.Max(c => c.UpdatedAt)
                };

                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting market stats");
                return StatusCode(500, new { message = "خطأ في جلب إحصائيات السوق" });
            }
        }

        /// <summary>
        /// فحص حالة APIs
        /// </summary>
        [HttpGet("api-status")]
        public async Task<IActionResult> GetApiStatus()
        {
            try
            {
                var status = await _dataCollectionService.CheckApiStatusAsync();
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking API status");
                return StatusCode(500, new { message = "خطأ في فحص حالة APIs" });
            }
        }
    }
}
