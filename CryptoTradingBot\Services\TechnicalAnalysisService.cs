using CryptoTradingBot.Models;
using CryptoTradingBot.Data;
using Microsoft.EntityFrameworkCore;

namespace CryptoTradingBot.Services
{
    /// <summary>
    /// خدمة التحليل الفني
    /// </summary>
    public class TechnicalAnalysisService : ITechnicalAnalysisService
    {
        private readonly CryptoTradingDbContext _context;
        private readonly ILogger<TechnicalAnalysisService> _logger;

        public TechnicalAnalysisService(CryptoTradingDbContext context, ILogger<TechnicalAnalysisService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public List<decimal> CalculateSimpleMovingAverage(List<decimal> prices, int period)
        {
            var sma = new List<decimal>();
            
            for (int i = 0; i < prices.Count; i++)
            {
                if (i < period - 1)
                {
                    sma.Add(0); // لا توجد بيانات كافية
                }
                else
                {
                    var sum = prices.Skip(i - period + 1).Take(period).Sum();
                    sma.Add(sum / period);
                }
            }

            return sma;
        }

        public List<decimal> CalculateExponentialMovingAverage(List<decimal> prices, int period)
        {
            var ema = new List<decimal>();
            var multiplier = 2m / (period + 1);

            for (int i = 0; i < prices.Count; i++)
            {
                if (i == 0)
                {
                    ema.Add(prices[i]);
                }
                else
                {
                    var currentEma = (prices[i] * multiplier) + (ema[i - 1] * (1 - multiplier));
                    ema.Add(currentEma);
                }
            }

            return ema;
        }

        public List<decimal> CalculateRSI(List<decimal> prices, int period = 14)
        {
            var rsi = new List<decimal>();
            var gains = new List<decimal>();
            var losses = new List<decimal>();

            // حساب المكاسب والخسائر
            for (int i = 1; i < prices.Count; i++)
            {
                var change = prices[i] - prices[i - 1];
                gains.Add(change > 0 ? change : 0);
                losses.Add(change < 0 ? Math.Abs(change) : 0);
            }

            // حساب RSI
            for (int i = 0; i < gains.Count; i++)
            {
                if (i < period - 1)
                {
                    rsi.Add(50); // قيمة افتراضية
                }
                else
                {
                    var avgGain = gains.Skip(i - period + 1).Take(period).Average();
                    var avgLoss = losses.Skip(i - period + 1).Take(period).Average();

                    if (avgLoss == 0)
                    {
                        rsi.Add(100);
                    }
                    else
                    {
                        var rs = avgGain / avgLoss;
                        var rsiValue = 100 - (100 / (1 + rs));
                        rsi.Add(rsiValue);
                    }
                }
            }

            return rsi;
        }

        public MACDResult CalculateMACD(List<decimal> prices, int fastPeriod = 12, int slowPeriod = 26, int signalPeriod = 9)
        {
            var fastEMA = CalculateExponentialMovingAverage(prices, fastPeriod);
            var slowEMA = CalculateExponentialMovingAverage(prices, slowPeriod);

            var macdLine = new List<decimal>();
            for (int i = 0; i < prices.Count; i++)
            {
                macdLine.Add(fastEMA[i] - slowEMA[i]);
            }

            var signalLine = CalculateExponentialMovingAverage(macdLine, signalPeriod);
            var histogram = new List<decimal>();

            for (int i = 0; i < macdLine.Count; i++)
            {
                histogram.Add(macdLine[i] - signalLine[i]);
            }

            return new MACDResult
            {
                MACDLine = macdLine,
                SignalLine = signalLine,
                Histogram = histogram
            };
        }

        public BollingerBandsResult CalculateBollingerBands(List<decimal> prices, int period = 20, decimal standardDeviations = 2)
        {
            var sma = CalculateSimpleMovingAverage(prices, period);
            var upperBand = new List<decimal>();
            var lowerBand = new List<decimal>();

            for (int i = 0; i < prices.Count; i++)
            {
                if (i < period - 1)
                {
                    upperBand.Add(prices[i]);
                    lowerBand.Add(prices[i]);
                }
                else
                {
                    var priceSlice = prices.Skip(i - period + 1).Take(period).ToList();
                    var mean = priceSlice.Average();
                    var variance = priceSlice.Select(x => (x - mean) * (x - mean)).Average();
                    var stdDev = (decimal)Math.Sqrt((double)variance);

                    upperBand.Add(sma[i] + (standardDeviations * stdDev));
                    lowerBand.Add(sma[i] - (standardDeviations * stdDev));
                }
            }

            return new BollingerBandsResult
            {
                UpperBand = upperBand,
                MiddleBand = sma,
                LowerBand = lowerBand
            };
        }

        public SupportResistanceResult FindSupportResistanceLevels(List<PriceHistory> priceHistory)
        {
            var lows = priceHistory.Select(p => p.Low).OrderBy(x => x).ToList();
            var highs = priceHistory.Select(p => p.High).OrderByDescending(x => x).ToList();
            var currentPrice = priceHistory.LastOrDefault()?.Close ?? 0;

            // العثور على مستويات الدعم (أدنى النقاط)
            var supportLevels = lows.Take(3).ToList();

            // العثور على مستويات المقاومة (أعلى النقاط)
            var resistanceLevels = highs.Take(3).ToList();

            var nearestSupport = supportLevels.Where(s => s < currentPrice).DefaultIfEmpty(0).Max();
            var nearestResistance = resistanceLevels.Where(r => r > currentPrice).DefaultIfEmpty(decimal.MaxValue).Min();

            return new SupportResistanceResult
            {
                SupportLevels = supportLevels,
                ResistanceLevels = resistanceLevels,
                CurrentPrice = currentPrice,
                NearestSupport = nearestSupport,
                NearestResistance = nearestResistance == decimal.MaxValue ? 0 : nearestResistance
            };
        }

        public TrendAnalysisResult AnalyzeTrend(List<PriceHistory> priceHistory)
        {
            var prices = priceHistory.Select(p => p.Close).ToList();
            
            // تحليل الاتجاه قصير المدى (10 فترات)
            var shortTermTrend = AnalyzeTrendDirection(prices.TakeLast(10).ToList());
            
            // تحليل الاتجاه متوسط المدى (30 فترة)
            var mediumTermTrend = AnalyzeTrendDirection(prices.TakeLast(30).ToList());
            
            // تحليل الاتجاه طويل المدى (50 فترة)
            var longTermTrend = AnalyzeTrendDirection(prices.TakeLast(50).ToList());

            // حساب قوة الاتجاه
            var trendStrength = CalculateTrendStrength(prices);

            // تحديد الاتجاه العام
            var overallTrend = DetermineOverallTrend(shortTermTrend, mediumTermTrend, longTermTrend);

            return new TrendAnalysisResult
            {
                ShortTermTrend = shortTermTrend,
                MediumTermTrend = mediumTermTrend,
                LongTermTrend = longTermTrend,
                TrendStrength = trendStrength,
                OverallTrend = overallTrend,
                TrendIndicators = GenerateTrendIndicators(shortTermTrend, mediumTermTrend, longTermTrend)
            };
        }

        public decimal CalculateVolatility(List<decimal> prices, int period = 20)
        {
            if (prices.Count < period)
                return 0;

            var returns = new List<decimal>();
            for (int i = 1; i < prices.Count; i++)
            {
                var returnValue = (prices[i] - prices[i - 1]) / prices[i - 1];
                returns.Add(returnValue);
            }

            var recentReturns = returns.TakeLast(period).ToList();
            var mean = recentReturns.Average();
            var variance = recentReturns.Select(r => (r - mean) * (r - mean)).Average();
            
            return (decimal)Math.Sqrt((double)variance) * 100; // تحويل إلى نسبة مئوية
        }

        public async Task<ComprehensiveTechnicalAnalysis> GetComprehensiveAnalysisAsync(string symbol)
        {
            try
            {
                var currency = await _context.CryptoCurrencies
                    .FirstOrDefaultAsync(c => c.Symbol == symbol && c.IsActive);

                if (currency == null)
                {
                    throw new ArgumentException($"Currency {symbol} not found");
                }

                var priceHistory = await _context.PriceHistories
                    .Where(p => p.CryptoCurrencyId == currency.Id)
                    .OrderBy(p => p.Timestamp)
                    .Take(200)
                    .ToListAsync();

                if (!priceHistory.Any())
                {
                    throw new InvalidOperationException($"No price history found for {symbol}");
                }

                var prices = priceHistory.Select(p => p.Close).ToList();
                var currentPrice = prices.LastOrDefault();

                // حساب المؤشرات الفنية
                var rsi = CalculateRSI(prices);
                var macd = CalculateMACD(prices);
                var bollingerBands = CalculateBollingerBands(prices);
                var sma20 = CalculateSimpleMovingAverage(prices, 20);
                var sma50 = CalculateSimpleMovingAverage(prices, 50);
                var ema12 = CalculateExponentialMovingAverage(prices, 12);
                var ema26 = CalculateExponentialMovingAverage(prices, 26);

                // التحليلات المتقدمة
                var trendAnalysis = AnalyzeTrend(priceHistory);
                var supportResistance = FindSupportResistanceLevels(priceHistory);
                var volatility = CalculateVolatility(prices);

                // توليد الإشارات
                var signals = GenerateTechnicalSignals(rsi.LastOrDefault(), macd, currentPrice, sma20.LastOrDefault(), sma50.LastOrDefault());

                // التقييم العام
                var overallSignal = DetermineOverallSignal(signals, trendAnalysis);
                var signalStrength = CalculateSignalStrength(signals);

                return new ComprehensiveTechnicalAnalysis
                {
                    Symbol = symbol,
                    CurrentPrice = currentPrice,
                    RSI = rsi.LastOrDefault(),
                    MACD = macd,
                    BollingerBands = bollingerBands,
                    SMA20 = sma20.LastOrDefault(),
                    SMA50 = sma50.LastOrDefault(),
                    EMA12 = ema12.LastOrDefault(),
                    EMA26 = ema26.LastOrDefault(),
                    TrendAnalysis = trendAnalysis,
                    SupportResistance = supportResistance,
                    Volatility = volatility,
                    Signals = signals,
                    OverallSignal = overallSignal,
                    SignalStrength = signalStrength,
                    Summary = GenerateAnalysisSummary(overallSignal, signalStrength, trendAnalysis)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error performing comprehensive analysis for {symbol}");
                throw;
            }
        }

        // دوال مساعدة خاصة
        private string AnalyzeTrendDirection(List<decimal> prices)
        {
            if (prices.Count < 2) return "SIDEWAYS";

            var firstPrice = prices.First();
            var lastPrice = prices.Last();
            var change = (lastPrice - firstPrice) / firstPrice;

            return change switch
            {
                > 0.02m => "BULLISH",
                < -0.02m => "BEARISH",
                _ => "SIDEWAYS"
            };
        }

        private decimal CalculateTrendStrength(List<decimal> prices)
        {
            if (prices.Count < 10) return 0.5m;

            var changes = new List<decimal>();
            for (int i = 1; i < prices.Count; i++)
            {
                changes.Add((prices[i] - prices[i - 1]) / prices[i - 1]);
            }

            var positiveChanges = changes.Count(c => c > 0);
            var strength = (decimal)positiveChanges / changes.Count;

            return Math.Abs(strength - 0.5m) * 2; // تحويل إلى قوة من 0 إلى 1
        }

        private string DetermineOverallTrend(string shortTerm, string mediumTerm, string longTerm)
        {
            var trends = new[] { shortTerm, mediumTerm, longTerm };
            var bullishCount = trends.Count(t => t == "BULLISH");
            var bearishCount = trends.Count(t => t == "BEARISH");

            return bullishCount > bearishCount ? "BULLISH" : 
                   bearishCount > bullishCount ? "BEARISH" : "SIDEWAYS";
        }

        private List<string> GenerateTrendIndicators(string shortTerm, string mediumTerm, string longTerm)
        {
            var indicators = new List<string>();

            if (shortTerm == "BULLISH") indicators.Add("Short-term uptrend");
            if (mediumTerm == "BULLISH") indicators.Add("Medium-term uptrend");
            if (longTerm == "BULLISH") indicators.Add("Long-term uptrend");

            if (shortTerm == "BEARISH") indicators.Add("Short-term downtrend");
            if (mediumTerm == "BEARISH") indicators.Add("Medium-term downtrend");
            if (longTerm == "BEARISH") indicators.Add("Long-term downtrend");

            return indicators;
        }

        private List<TechnicalSignal> GenerateTechnicalSignals(decimal rsi, MACDResult macd, decimal currentPrice, decimal sma20, decimal sma50)
        {
            var signals = new List<TechnicalSignal>();

            // إشارات RSI
            if (rsi < 30)
            {
                signals.Add(new TechnicalSignal
                {
                    Type = "RSI_OVERSOLD",
                    Signal = "BUY",
                    Strength = 0.7m,
                    Description = "RSI indicates oversold condition"
                });
            }
            else if (rsi > 70)
            {
                signals.Add(new TechnicalSignal
                {
                    Type = "RSI_OVERBOUGHT",
                    Signal = "SELL",
                    Strength = 0.7m,
                    Description = "RSI indicates overbought condition"
                });
            }

            // إشارات المتوسطات المتحركة
            if (currentPrice > sma20 && sma20 > sma50)
            {
                signals.Add(new TechnicalSignal
                {
                    Type = "MA_BULLISH",
                    Signal = "BUY",
                    Strength = 0.6m,
                    Description = "Price above moving averages - bullish signal"
                });
            }
            else if (currentPrice < sma20 && sma20 < sma50)
            {
                signals.Add(new TechnicalSignal
                {
                    Type = "MA_BEARISH",
                    Signal = "SELL",
                    Strength = 0.6m,
                    Description = "Price below moving averages - bearish signal"
                });
            }

            return signals;
        }

        private string DetermineOverallSignal(List<TechnicalSignal> signals, TrendAnalysisResult trendAnalysis)
        {
            var buySignals = signals.Count(s => s.Signal == "BUY");
            var sellSignals = signals.Count(s => s.Signal == "SELL");

            // إعطاء وزن إضافي للاتجاه العام
            if (trendAnalysis.OverallTrend == "BULLISH") buySignals++;
            if (trendAnalysis.OverallTrend == "BEARISH") sellSignals++;

            return buySignals > sellSignals ? "BUY" :
                   sellSignals > buySignals ? "SELL" : "HOLD";
        }

        private decimal CalculateSignalStrength(List<TechnicalSignal> signals)
        {
            if (!signals.Any()) return 0.5m;

            return signals.Average(s => s.Strength);
        }

        private string GenerateAnalysisSummary(string overallSignal, decimal signalStrength, TrendAnalysisResult trendAnalysis)
        {
            var strength = signalStrength switch
            {
                > 0.8m => "قوية جداً",
                > 0.6m => "قوية",
                > 0.4m => "متوسطة",
                _ => "ضعيفة"
            };

            return $"الإشارة العامة: {overallSignal} بقوة {strength}. الاتجاه العام: {trendAnalysis.OverallTrend}";
        }
    }
}
