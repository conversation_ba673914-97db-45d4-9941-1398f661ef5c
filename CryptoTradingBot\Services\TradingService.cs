using CryptoTradingBot.Models;
using CryptoTradingBot.Data;
using Microsoft.EntityFrameworkCore;

namespace CryptoTradingBot.Services
{
    /// <summary>
    /// خدمة التداول الرئيسية
    /// </summary>
    public class TradingService : ITradingService
    {
        private readonly CryptoTradingDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<TradingService> _logger;

        public TradingService(
            CryptoTradingDbContext context,
            IConfiguration configuration,
            ILogger<TradingService> logger)
        {
            _context = context;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<TradingResult> ExecuteTradeAsync(TradingOperation operation)
        {
            try
            {
                // التحقق من صحة العملية
                var validationResult = await ValidateTradeOperation(operation);
                if (!validationResult.IsValid)
                {
                    return new TradingResult
                    {
                        Success = false,
                        Message = validationResult.ErrorMessage
                    };
                }

                // تحديد السعر الحالي
                var currentPrice = await GetCurrentPrice(operation.CryptoCurrencyId);
                if (currentPrice == 0)
                {
                    return new TradingResult
                    {
                        Success = false,
                        Message = "لا يمكن الحصول على السعر الحالي"
                    };
                }

                // تحديث تفاصيل العملية
                operation.Price = currentPrice;
                operation.TotalValue = operation.Amount * currentPrice;
                operation.Fee = CalculateFee(operation.TotalValue);
                operation.ExecutedAt = DateTime.UtcNow;

                // في وضع Paper Trading
                if (operation.Mode == "PAPER")
                {
                    operation.Status = "COMPLETED";
                    _context.TradingOperations.Add(operation);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation($"Paper trade executed: {operation.Type} {operation.Amount} of {operation.CryptoCurrency?.Symbol} at ${currentPrice}");

                    return new TradingResult
                    {
                        Success = true,
                        Message = "تم تنفيذ الصفقة الوهمية بنجاح",
                        OperationId = operation.Id,
                        ExecutedPrice = currentPrice,
                        Fee = operation.Fee,
                        ExecutionTime = operation.ExecutedAt
                    };
                }
                else
                {
                    // في وضع Live Trading - يتطلب تكامل مع بورصة حقيقية
                    return await ExecuteLiveTrade(operation, currentPrice);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing trade");
                return new TradingResult
                {
                    Success = false,
                    Message = "خطأ في تنفيذ الصفقة"
                };
            }
        }

        public async Task<List<TradingOperation>> GetActiveTradesAsync()
        {
            try
            {
                return await _context.TradingOperations
                    .Include(t => t.CryptoCurrency)
                    .Where(t => t.Status == "COMPLETED" && t.Type == "BUY")
                    .OrderByDescending(t => t.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active trades");
                return new List<TradingOperation>();
            }
        }

        public async Task<bool> CancelTradeAsync(long operationId)
        {
            try
            {
                var operation = await _context.TradingOperations
                    .FirstOrDefaultAsync(t => t.Id == operationId);

                if (operation == null)
                {
                    return false;
                }

                if (operation.Status != "PENDING")
                {
                    return false;
                }

                operation.Status = "CANCELLED";
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Trade {operationId} cancelled");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error cancelling trade {operationId}");
                return false;
            }
        }

        public decimal CalculateProfitLoss(TradingOperation operation, decimal currentPrice)
        {
            if (operation.Type == "BUY")
            {
                return (currentPrice - operation.Price) * operation.Amount;
            }
            else if (operation.Type == "SELL")
            {
                return (operation.Price - currentPrice) * operation.Amount;
            }

            return 0;
        }

        public async Task<bool> ShouldCloseTrade(TradingOperation operation, decimal currentPrice)
        {
            try
            {
                var userSettings = await _context.UserSettings
                    .FirstOrDefaultAsync(s => s.UserId == "default"); // يمكن تحسينه لاحقاً

                if (userSettings == null)
                    return false;

                var profitLoss = CalculateProfitLoss(operation, currentPrice);
                var profitLossPercentage = (profitLoss / operation.TotalValue) * 100;

                // فحص Stop Loss
                if (profitLossPercentage <= -userSettings.GlobalStopLoss)
                {
                    _logger.LogInformation($"Stop loss triggered for trade {operation.Id}: {profitLossPercentage:F2}%");
                    return true;
                }

                // فحص Take Profit
                if (profitLossPercentage >= userSettings.GlobalTakeProfit)
                {
                    _logger.LogInformation($"Take profit triggered for trade {operation.Id}: {profitLossPercentage:F2}%");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking if trade {operation.Id} should be closed");
                return false;
            }
        }

        public async Task<TradingStatistics> GetTradingStatisticsAsync(string userId)
        {
            try
            {
                var trades = await _context.TradingOperations
                    .Where(t => t.Status == "COMPLETED")
                    .ToListAsync();

                var statistics = new TradingStatistics
                {
                    TotalTrades = trades.Count,
                    ActiveTrades = await _context.TradingOperations
                        .CountAsync(t => t.Status == "COMPLETED" && t.Type == "BUY")
                };

                if (trades.Any())
                {
                    var profits = trades.Where(t => t.Profit.HasValue && t.Profit > 0).ToList();
                    var losses = trades.Where(t => t.Profit.HasValue && t.Profit < 0).ToList();

                    statistics.WinningTrades = profits.Count;
                    statistics.LosingTrades = losses.Count;
                    statistics.TotalProfit = profits.Sum(t => t.Profit ?? 0);
                    statistics.TotalLoss = Math.Abs(losses.Sum(t => t.Profit ?? 0));
                    statistics.WinRate = statistics.TotalTrades > 0 
                        ? (decimal)statistics.WinningTrades / statistics.TotalTrades * 100 
                        : 0;

                    if (profits.Any())
                        statistics.AverageProfit = statistics.TotalProfit / profits.Count;

                    if (losses.Any())
                        statistics.AverageLoss = statistics.TotalLoss / losses.Count;

                    statistics.TotalBalance = 10000 + statistics.TotalProfit - statistics.TotalLoss; // رصيد افتراضي
                }

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating trading statistics");
                return new TradingStatistics();
            }
        }

        public async Task UpdateActiveTradesAsync()
        {
            try
            {
                var activeTrades = await GetActiveTradesAsync();
                
                foreach (var trade in activeTrades)
                {
                    var currentPrice = await GetCurrentPrice(trade.CryptoCurrencyId);
                    if (currentPrice > 0)
                    {
                        trade.Profit = CalculateProfitLoss(trade, currentPrice);
                        
                        if (await ShouldCloseTrade(trade, currentPrice))
                        {
                            // إنشاء صفقة بيع مقابلة
                            var sellOperation = new TradingOperation
                            {
                                CryptoCurrencyId = trade.CryptoCurrencyId,
                                Type = "SELL",
                                Amount = trade.Amount,
                                Price = currentPrice,
                                TotalValue = trade.Amount * currentPrice,
                                Fee = CalculateFee(trade.Amount * currentPrice),
                                Status = "COMPLETED",
                                Mode = trade.Mode,
                                Strategy = "AUTO_CLOSE",
                                ExecutedAt = DateTime.UtcNow,
                                Profit = trade.Profit
                            };

                            _context.TradingOperations.Add(sellOperation);
                            _logger.LogInformation($"Auto-closed trade {trade.Id} with profit/loss: ${trade.Profit:F2}");
                        }
                    }
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating active trades");
            }
        }

        private async Task<(bool IsValid, string ErrorMessage)> ValidateTradeOperation(TradingOperation operation)
        {
            // التحقق من وجود العملة
            var currency = await _context.CryptoCurrencies
                .FirstOrDefaultAsync(c => c.Id == operation.CryptoCurrencyId);

            if (currency == null)
            {
                return (false, "العملة المشفرة غير موجودة");
            }

            // التحقق من المبلغ
            var minAmount = _configuration.GetValue<decimal>("TradingSettings:MinTradeAmount", 10);
            var maxAmount = _configuration.GetValue<decimal>("TradingSettings:MaxTradeAmount", 1000);

            if (operation.Amount < minAmount)
            {
                return (false, $"المبلغ أقل من الحد الأدنى: ${minAmount}");
            }

            if (operation.Amount > maxAmount)
            {
                return (false, $"المبلغ أكبر من الحد الأقصى: ${maxAmount}");
            }

            return (true, string.Empty);
        }

        private async Task<decimal> GetCurrentPrice(int currencyId)
        {
            try
            {
                var currency = await _context.CryptoCurrencies
                    .FirstOrDefaultAsync(c => c.Id == currencyId);

                return currency?.CurrentPrice ?? 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting current price for currency {currencyId}");
                return 0;
            }
        }

        private decimal CalculateFee(decimal totalValue)
        {
            // رسوم افتراضية 0.1%
            return totalValue * 0.001m;
        }

        private async Task<TradingResult> ExecuteLiveTrade(TradingOperation operation, decimal currentPrice)
        {
            // هنا يتم تكامل مع بورصة حقيقية مثل Binance
            // للآن سنعيد نتيجة وهمية
            await Task.Delay(1000);

            operation.Status = "COMPLETED";
            _context.TradingOperations.Add(operation);
            await _context.SaveChangesAsync();

            return new TradingResult
            {
                Success = true,
                Message = "تم تنفيذ الصفقة الحقيقية بنجاح",
                OperationId = operation.Id,
                ExecutedPrice = currentPrice,
                Fee = operation.Fee,
                ExecutionTime = operation.ExecutedAt
            };
        }
    }
}
