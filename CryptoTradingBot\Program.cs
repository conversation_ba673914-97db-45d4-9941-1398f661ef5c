using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using CryptoTradingBot.Data;
using CryptoTradingBot.Services;

using StackExchange.Redis;

var builder = WebApplication.CreateBuilder(args);

// إضافة خدمات قاعدة البيانات
builder.Services.AddDbContext<CryptoTradingDbContext>(options =>
    options.UseSqlite(builder.Configuration.GetConnectionString("DefaultConnection")));

// إضافة Redis للتخزين المؤقت
builder.Services.AddSingleton<IConnectionMultiplexer>(provider =>
{
    var connectionString = builder.Configuration.GetConnectionString("Redis");
    return ConnectionMultiplexer.Connect(connectionString ?? "localhost:6379");
});

// إضافة خدمات المصادقة والتشفير
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"] ?? "DefaultSecretKey123456789"))
        };
    });

builder.Services.AddAuthorization();

// إضافة خدمات CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// إضافة HttpClient
builder.Services.AddHttpClient();

// إضافة Controllers
builder.Services.AddControllers();

// إضافة OpenAPI/Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// إضافة خدمات مخصصة
builder.Services.AddScoped<IEncryptionService, EncryptionService>();
builder.Services.AddScoped<IDataCollectionService, DataCollectionService>();
builder.Services.AddScoped<IDeepSeekService, DeepSeekService>();
builder.Services.AddScoped<ITradingService, TradingService>();
builder.Services.AddScoped<INewsAnalysisService, NewsAnalysisService>();
builder.Services.AddScoped<ITechnicalAnalysisService, TechnicalAnalysisService>();

// إضافة خدمات الخلفية
builder.Services.AddHostedService<DataCollectionBackgroundService>();
builder.Services.AddHostedService<TradingBackgroundService>();

var app = builder.Build();

// تكوين pipeline الطلبات
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseStaticFiles(); // إضافة دعم الملفات الثابتة
app.UseCors("AllowAll");
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// إضافة route افتراضي للصفحة الرئيسية
app.MapFallbackToFile("index.html");

// إنشاء قاعدة البيانات إذا لم تكن موجودة
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<CryptoTradingDbContext>();
    context.Database.EnsureCreated();
}

app.Run();
