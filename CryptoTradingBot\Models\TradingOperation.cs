using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CryptoTradingBot.Models
{
    /// <summary>
    /// نموذج عمليات التداول
    /// </summary>
    public class TradingOperation
    {
        [Key]
        public long Id { get; set; }

        [Required]
        public int CryptoCurrencyId { get; set; }

        [Required]
        [StringLength(10)]
        public string Type { get; set; } = string.Empty; // BUY, SELL

        [Column(TypeName = "decimal(18,8)")]
        public decimal Amount { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal Price { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal TotalValue { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal Fee { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal? Profit { get; set; }

        [StringLength(20)]
        public string Status { get; set; } = "PENDING"; // PENDING, COMPLETED, CANCELLED, FAILED

        [StringLength(20)]
        public string Mode { get; set; } = "PAPER"; // PAPER, LIVE

        [StringLength(50)]
        public string? Exchange { get; set; }

        [StringLength(100)]
        public string? ExternalOrderId { get; set; }

        [StringLength(50)]
        public string? Strategy { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? ExecutedAt { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        // العلاقات
        [ForeignKey("CryptoCurrencyId")]
        public virtual CryptoCurrency CryptoCurrency { get; set; } = null!;
    }
}
