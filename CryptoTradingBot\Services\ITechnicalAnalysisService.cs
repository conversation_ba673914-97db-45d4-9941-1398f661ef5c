using CryptoTradingBot.Models;

namespace CryptoTradingBot.Services
{
    /// <summary>
    /// واجهة خدمة التحليل الفني
    /// </summary>
    public interface ITechnicalAnalysisService
    {
        /// <summary>
        /// حساب المتوسط المتحرك البسيط
        /// </summary>
        /// <param name="prices">قائمة الأسعار</param>
        /// <param name="period">الفترة</param>
        /// <returns>قائمة المتوسطات المتحركة</returns>
        List<decimal> CalculateSimpleMovingAverage(List<decimal> prices, int period);

        /// <summary>
        /// حساب المتوسط المتحرك الأسي
        /// </summary>
        /// <param name="prices">قائمة الأسعار</param>
        /// <param name="period">الفترة</param>
        /// <returns>قائمة المتوسطات المتحركة الأسية</returns>
        List<decimal> CalculateExponentialMovingAverage(List<decimal> prices, int period);

        /// <summary>
        /// حساب مؤشر القوة النسبية RSI
        /// </summary>
        /// <param name="prices">قائمة الأسعار</param>
        /// <param name="period">الفترة (افتراضي 14)</param>
        /// <returns>قائمة قيم RSI</returns>
        List<decimal> CalculateRSI(List<decimal> prices, int period = 14);

        /// <summary>
        /// حساب مؤشر MACD
        /// </summary>
        /// <param name="prices">قائمة الأسعار</param>
        /// <param name="fastPeriod">الفترة السريعة</param>
        /// <param name="slowPeriod">الفترة البطيئة</param>
        /// <param name="signalPeriod">فترة الإشارة</param>
        /// <returns>نتائج MACD</returns>
        MACDResult CalculateMACD(List<decimal> prices, int fastPeriod = 12, int slowPeriod = 26, int signalPeriod = 9);

        /// <summary>
        /// حساب البولينجر باندز
        /// </summary>
        /// <param name="prices">قائمة الأسعار</param>
        /// <param name="period">الفترة</param>
        /// <param name="standardDeviations">عدد الانحرافات المعيارية</param>
        /// <returns>نتائج البولينجر باندز</returns>
        BollingerBandsResult CalculateBollingerBands(List<decimal> prices, int period = 20, decimal standardDeviations = 2);

        /// <summary>
        /// تحديد مستويات الدعم والمقاومة
        /// </summary>
        /// <param name="priceHistory">البيانات التاريخية</param>
        /// <returns>مستويات الدعم والمقاومة</returns>
        SupportResistanceResult FindSupportResistanceLevels(List<PriceHistory> priceHistory);

        /// <summary>
        /// تحليل الاتجاه العام
        /// </summary>
        /// <param name="priceHistory">البيانات التاريخية</param>
        /// <returns>تحليل الاتجاه</returns>
        TrendAnalysisResult AnalyzeTrend(List<PriceHistory> priceHistory);

        /// <summary>
        /// حساب التقلبات (Volatility)
        /// </summary>
        /// <param name="prices">قائمة الأسعار</param>
        /// <param name="period">الفترة</param>
        /// <returns>قيمة التقلبات</returns>
        decimal CalculateVolatility(List<decimal> prices, int period = 20);

        /// <summary>
        /// تحليل شامل للعملة
        /// </summary>
        /// <param name="symbol">رمز العملة</param>
        /// <returns>تحليل فني شامل</returns>
        Task<ComprehensiveTechnicalAnalysis> GetComprehensiveAnalysisAsync(string symbol);
    }

    /// <summary>
    /// نموذج نتائج MACD
    /// </summary>
    public class MACDResult
    {
        public List<decimal> MACDLine { get; set; } = new();
        public List<decimal> SignalLine { get; set; } = new();
        public List<decimal> Histogram { get; set; } = new();
    }

    /// <summary>
    /// نموذج نتائج البولينجر باندز
    /// </summary>
    public class BollingerBandsResult
    {
        public List<decimal> UpperBand { get; set; } = new();
        public List<decimal> MiddleBand { get; set; } = new();
        public List<decimal> LowerBand { get; set; } = new();
    }

    /// <summary>
    /// نموذج مستويات الدعم والمقاومة
    /// </summary>
    public class SupportResistanceResult
    {
        public List<decimal> SupportLevels { get; set; } = new();
        public List<decimal> ResistanceLevels { get; set; } = new();
        public decimal CurrentPrice { get; set; }
        public decimal NearestSupport { get; set; }
        public decimal NearestResistance { get; set; }
    }

    /// <summary>
    /// نموذج تحليل الاتجاه
    /// </summary>
    public class TrendAnalysisResult
    {
        public string ShortTermTrend { get; set; } = string.Empty; // BULLISH, BEARISH, SIDEWAYS
        public string MediumTermTrend { get; set; } = string.Empty;
        public string LongTermTrend { get; set; } = string.Empty;
        public decimal TrendStrength { get; set; } // 0-1
        public string OverallTrend { get; set; } = string.Empty;
        public List<string> TrendIndicators { get; set; } = new();
    }

    /// <summary>
    /// نموذج التحليل الفني الشامل
    /// </summary>
    public class ComprehensiveTechnicalAnalysis
    {
        public string Symbol { get; set; } = string.Empty;
        public decimal CurrentPrice { get; set; }
        public DateTime AnalysisTime { get; set; } = DateTime.UtcNow;

        // المؤشرات الفنية
        public decimal RSI { get; set; }
        public MACDResult MACD { get; set; } = new();
        public BollingerBandsResult BollingerBands { get; set; } = new();
        public decimal SMA20 { get; set; }
        public decimal SMA50 { get; set; }
        public decimal EMA12 { get; set; }
        public decimal EMA26 { get; set; }

        // تحليل الاتجاه
        public TrendAnalysisResult TrendAnalysis { get; set; } = new();

        // الدعم والمقاومة
        public SupportResistanceResult SupportResistance { get; set; } = new();

        // التقلبات
        public decimal Volatility { get; set; }

        // الإشارات
        public List<TechnicalSignal> Signals { get; set; } = new();

        // التقييم العام
        public string OverallSignal { get; set; } = string.Empty; // BUY, SELL, HOLD
        public decimal SignalStrength { get; set; } // 0-1
        public string Summary { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج الإشارة الفنية
    /// </summary>
    public class TechnicalSignal
    {
        public string Type { get; set; } = string.Empty; // RSI_OVERSOLD, MACD_BULLISH_CROSSOVER, etc.
        public string Signal { get; set; } = string.Empty; // BUY, SELL, NEUTRAL
        public decimal Strength { get; set; } // 0-1
        public string Description { get; set; } = string.Empty;
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }
}
