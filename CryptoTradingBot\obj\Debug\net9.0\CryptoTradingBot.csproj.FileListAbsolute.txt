C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\appsettings.Development.json
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\appsettings.json
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\CryptoTradingBot.staticwebassets.runtime.json
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\CryptoTradingBot.staticwebassets.endpoints.json
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\CryptoTradingBot.exe
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\CryptoTradingBot.deps.json
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\CryptoTradingBot.runtimeconfig.json
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\CryptoTradingBot.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\CryptoTradingBot.pdb
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\DeepSeek.Core.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\CCXT.NET.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\CellWars.Threading.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.AspNetCore.OpenApi.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Data.Sqlite.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Relational.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Sqlite.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.OpenApi.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Newtonsoft.Json.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Npgsql.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Npgsql.EntityFrameworkCore.PostgreSQL.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Pipelines.Sockets.Unofficial.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\RestSharp.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\SQLitePCLRaw.batteries_v2.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\SQLitePCLRaw.core.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\SQLitePCLRaw.provider.e_sqlite3.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\StackExchange.Redis.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\System.Text.Json.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\browser-wasm\nativeassets\net9.0\e_sqlite3.a
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-arm\native\libe_sqlite3.so
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-arm64\native\libe_sqlite3.so
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-armel\native\libe_sqlite3.so
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-mips64\native\libe_sqlite3.so
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-musl-arm\native\libe_sqlite3.so
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-musl-arm64\native\libe_sqlite3.so
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-musl-s390x\native\libe_sqlite3.so
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-musl-x64\native\libe_sqlite3.so
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-ppc64le\native\libe_sqlite3.so
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-s390x\native\libe_sqlite3.so
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-x64\native\libe_sqlite3.so
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-x86\native\libe_sqlite3.so
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\osx-arm64\native\libe_sqlite3.dylib
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\osx-x64\native\libe_sqlite3.dylib
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\win-arm\native\e_sqlite3.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\win-arm64\native\e_sqlite3.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\win-x64\native\e_sqlite3.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\win-x86\native\e_sqlite3.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.csproj.AssemblyReference.cache
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\rpswa.dswa.cache.json
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.AssemblyInfoInputs.cache
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.AssemblyInfo.cs
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.csproj.CoreCompileInputs.cache
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\rjimswa.dswa.cache.json
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\rjsmrazor.dswa.cache.json
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\rjsmcshtml.dswa.cache.json
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\scopedcss\bundle\CryptoTradingBot.styles.css
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\compressed\v4sb0oc98t-87naruipz6.gz
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\compressed\dff47ilxk8-u544at3w8a.gz
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\compressed\wqulb7x2k4-flty0zdg65.gz
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\staticwebassets.build.json
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\staticwebassets.build.json.cache
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\staticwebassets.development.json
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTr.03685CED.Up2Date
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\refint\CryptoTradingBot.dll
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.pdb
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.genruntimeconfig.cache
C:\Users\<USER>\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\ref\CryptoTradingBot.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\appsettings.Development.json
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\appsettings.json
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\CryptoTradingBot.staticwebassets.runtime.json
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\CryptoTradingBot.staticwebassets.endpoints.json
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\CryptoTradingBot.exe
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\DeepSeek.Core.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\CCXT.NET.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\CellWars.Threading.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.AspNetCore.OpenApi.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Data.Sqlite.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Abstractions.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Relational.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Sqlite.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.Caching.Abstractions.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.Caching.Memory.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.Configuration.Abstractions.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.DependencyModel.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.Logging.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.Logging.Abstractions.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.Options.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.Extensions.Primitives.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.IdentityModel.Abstractions.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.IdentityModel.JsonWebTokens.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.IdentityModel.Logging.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.IdentityModel.Protocols.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.IdentityModel.Tokens.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Microsoft.OpenApi.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Newtonsoft.Json.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Npgsql.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Npgsql.EntityFrameworkCore.PostgreSQL.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Pipelines.Sockets.Unofficial.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\RestSharp.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\SQLitePCLRaw.batteries_v2.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\SQLitePCLRaw.core.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\SQLitePCLRaw.provider.e_sqlite3.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\StackExchange.Redis.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Swashbuckle.AspNetCore.Swagger.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerGen.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerUI.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\System.IdentityModel.Tokens.Jwt.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\System.Text.Json.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\browser-wasm\nativeassets\net9.0\e_sqlite3.a
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-arm\native\libe_sqlite3.so
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-arm64\native\libe_sqlite3.so
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-armel\native\libe_sqlite3.so
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-mips64\native\libe_sqlite3.so
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-musl-arm\native\libe_sqlite3.so
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-musl-arm64\native\libe_sqlite3.so
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-musl-s390x\native\libe_sqlite3.so
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-musl-x64\native\libe_sqlite3.so
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-ppc64le\native\libe_sqlite3.so
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-s390x\native\libe_sqlite3.so
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-x64\native\libe_sqlite3.so
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\linux-x86\native\libe_sqlite3.so
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\osx-arm64\native\libe_sqlite3.dylib
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\osx-x64\native\libe_sqlite3.dylib
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\win-arm\native\e_sqlite3.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\win-arm64\native\e_sqlite3.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\win-x64\native\e_sqlite3.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\bin\Debug\net9.0\runtimes\win-x86\native\e_sqlite3.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.csproj.AssemblyReference.cache
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.GeneratedMSBuildEditorConfig.editorconfig
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.AssemblyInfoInputs.cache
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.AssemblyInfo.cs
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.csproj.CoreCompileInputs.cache
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.MvcApplicationPartsAssemblyInfo.cs
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.MvcApplicationPartsAssemblyInfo.cache
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\scopedcss\bundle\CryptoTradingBot.styles.css
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\compressed\v4sb0oc98t-87naruipz6.gz
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\compressed\dff47ilxk8-u544at3w8a.gz
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\compressed\wqulb7x2k4-flty0zdg65.gz
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\compressed\md0nozkbhh-sogzba1yik.gz
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\staticwebassets.build.json
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\staticwebassets.development.json
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\staticwebassets.build.endpoints.json
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\staticwebassets\msbuild.CryptoTradingBot.Microsoft.AspNetCore.StaticWebAssets.props
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\staticwebassets\msbuild.CryptoTradingBot.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\staticwebassets\msbuild.build.CryptoTradingBot.props
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\staticwebassets\msbuild.buildMultiTargeting.CryptoTradingBot.props
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\staticwebassets\msbuild.buildTransitive.CryptoTradingBot.props
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\staticwebassets.pack.json
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\staticwebassets.upToDateCheck.txt
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTr.03685CED.Up2Date
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\refint\CryptoTradingBot.dll
\\tsclient\C\Users\mahfu\OneDrive\Desktop\bot\CryptoTradingBot\obj\Debug\net9.0\CryptoTradingBot.pdb
